[{"model": "auth.permission", "pk": 1, "fields": {"name": "Can add log entry", "content_type": 1, "codename": "add_logentry"}}, {"model": "auth.permission", "pk": 2, "fields": {"name": "Can change log entry", "content_type": 1, "codename": "change_logentry"}}, {"model": "auth.permission", "pk": 3, "fields": {"name": "Can delete log entry", "content_type": 1, "codename": "delete_logentry"}}, {"model": "auth.permission", "pk": 4, "fields": {"name": "Can view log entry", "content_type": 1, "codename": "view_logentry"}}, {"model": "auth.permission", "pk": 5, "fields": {"name": "Can add permission", "content_type": 2, "codename": "add_permission"}}, {"model": "auth.permission", "pk": 6, "fields": {"name": "Can change permission", "content_type": 2, "codename": "change_permission"}}, {"model": "auth.permission", "pk": 7, "fields": {"name": "Can delete permission", "content_type": 2, "codename": "delete_permission"}}, {"model": "auth.permission", "pk": 8, "fields": {"name": "Can view permission", "content_type": 2, "codename": "view_permission"}}, {"model": "auth.permission", "pk": 9, "fields": {"name": "Can add group", "content_type": 3, "codename": "add_group"}}, {"model": "auth.permission", "pk": 10, "fields": {"name": "Can change group", "content_type": 3, "codename": "change_group"}}, {"model": "auth.permission", "pk": 11, "fields": {"name": "Can delete group", "content_type": 3, "codename": "delete_group"}}, {"model": "auth.permission", "pk": 12, "fields": {"name": "Can view group", "content_type": 3, "codename": "view_group"}}, {"model": "auth.permission", "pk": 13, "fields": {"name": "Can add content type", "content_type": 4, "codename": "add_contenttype"}}, {"model": "auth.permission", "pk": 14, "fields": {"name": "Can change content type", "content_type": 4, "codename": "change_contenttype"}}, {"model": "auth.permission", "pk": 15, "fields": {"name": "Can delete content type", "content_type": 4, "codename": "delete_contenttype"}}, {"model": "auth.permission", "pk": 16, "fields": {"name": "Can view content type", "content_type": 4, "codename": "view_contenttype"}}, {"model": "auth.permission", "pk": 17, "fields": {"name": "Can add session", "content_type": 5, "codename": "add_session"}}, {"model": "auth.permission", "pk": 18, "fields": {"name": "Can change session", "content_type": 5, "codename": "change_session"}}, {"model": "auth.permission", "pk": 19, "fields": {"name": "Can delete session", "content_type": 5, "codename": "delete_session"}}, {"model": "auth.permission", "pk": 20, "fields": {"name": "Can view session", "content_type": 5, "codename": "view_session"}}, {"model": "auth.permission", "pk": 21, "fields": {"name": "Can add 用户", "content_type": 6, "codename": "add_user"}}, {"model": "auth.permission", "pk": 22, "fields": {"name": "Can change 用户", "content_type": 6, "codename": "change_user"}}, {"model": "auth.permission", "pk": 23, "fields": {"name": "Can delete 用户", "content_type": 6, "codename": "delete_user"}}, {"model": "auth.permission", "pk": 24, "fields": {"name": "Can view 用户", "content_type": 6, "codename": "view_user"}}, {"model": "auth.permission", "pk": 25, "fields": {"name": "Can add 邮箱验证码", "content_type": 7, "codename": "add_emailverificationcode"}}, {"model": "auth.permission", "pk": 26, "fields": {"name": "Can change 邮箱验证码", "content_type": 7, "codename": "change_emailverificationcode"}}, {"model": "auth.permission", "pk": 27, "fields": {"name": "Can delete 邮箱验证码", "content_type": 7, "codename": "delete_emailverificationcode"}}, {"model": "auth.permission", "pk": 28, "fields": {"name": "Can view 邮箱验证码", "content_type": 7, "codename": "view_emailverificationcode"}}, {"model": "auth.permission", "pk": 29, "fields": {"name": "Can add 登录日志", "content_type": 8, "codename": "add_loginlog"}}, {"model": "auth.permission", "pk": 30, "fields": {"name": "Can change 登录日志", "content_type": 8, "codename": "change_loginlog"}}, {"model": "auth.permission", "pk": 31, "fields": {"name": "Can delete 登录日志", "content_type": 8, "codename": "delete_loginlog"}}, {"model": "auth.permission", "pk": 32, "fields": {"name": "Can view 登录日志", "content_type": 8, "codename": "view_loginlog"}}, {"model": "auth.permission", "pk": 33, "fields": {"name": "Can add 帖子", "content_type": 9, "codename": "add_post"}}, {"model": "auth.permission", "pk": 34, "fields": {"name": "Can change 帖子", "content_type": 9, "codename": "change_post"}}, {"model": "auth.permission", "pk": 35, "fields": {"name": "Can delete 帖子", "content_type": 9, "codename": "delete_post"}}, {"model": "auth.permission", "pk": 36, "fields": {"name": "Can view 帖子", "content_type": 9, "codename": "view_post"}}, {"model": "auth.permission", "pk": 37, "fields": {"name": "Can add 评论", "content_type": 10, "codename": "add_comment"}}, {"model": "auth.permission", "pk": 38, "fields": {"name": "Can change 评论", "content_type": 10, "codename": "change_comment"}}, {"model": "auth.permission", "pk": 39, "fields": {"name": "Can delete 评论", "content_type": 10, "codename": "delete_comment"}}, {"model": "auth.permission", "pk": 40, "fields": {"name": "Can view 评论", "content_type": 10, "codename": "view_comment"}}, {"model": "auth.permission", "pk": 41, "fields": {"name": "Can add 帖子图片", "content_type": 11, "codename": "add_postimage"}}, {"model": "auth.permission", "pk": 42, "fields": {"name": "Can change 帖子图片", "content_type": 11, "codename": "change_postimage"}}, {"model": "auth.permission", "pk": 43, "fields": {"name": "Can delete 帖子图片", "content_type": 11, "codename": "delete_postimage"}}, {"model": "auth.permission", "pk": 44, "fields": {"name": "Can view 帖子图片", "content_type": 11, "codename": "view_postimage"}}, {"model": "auth.permission", "pk": 45, "fields": {"name": "Can add 帖子点赞", "content_type": 12, "codename": "add_postlike"}}, {"model": "auth.permission", "pk": 46, "fields": {"name": "Can change 帖子点赞", "content_type": 12, "codename": "change_postlike"}}, {"model": "auth.permission", "pk": 47, "fields": {"name": "Can delete 帖子点赞", "content_type": 12, "codename": "delete_postlike"}}, {"model": "auth.permission", "pk": 48, "fields": {"name": "Can view 帖子点赞", "content_type": 12, "codename": "view_postlike"}}, {"model": "auth.permission", "pk": 49, "fields": {"name": "Can add 评论点赞", "content_type": 13, "codename": "add_commentlike"}}, {"model": "auth.permission", "pk": 50, "fields": {"name": "Can change 评论点赞", "content_type": 13, "codename": "change_commentlike"}}, {"model": "auth.permission", "pk": 51, "fields": {"name": "Can delete 评论点赞", "content_type": 13, "codename": "delete_commentlike"}}, {"model": "auth.permission", "pk": 52, "fields": {"name": "Can view 评论点赞", "content_type": 13, "codename": "view_commentlike"}}, {"model": "contenttypes.contenttype", "pk": 1, "fields": {"app_label": "admin", "model": "logentry"}}, {"model": "contenttypes.contenttype", "pk": 2, "fields": {"app_label": "auth", "model": "permission"}}, {"model": "contenttypes.contenttype", "pk": 3, "fields": {"app_label": "auth", "model": "group"}}, {"model": "contenttypes.contenttype", "pk": 4, "fields": {"app_label": "contenttypes", "model": "contenttype"}}, {"model": "contenttypes.contenttype", "pk": 5, "fields": {"app_label": "sessions", "model": "session"}}, {"model": "contenttypes.contenttype", "pk": 6, "fields": {"app_label": "users", "model": "user"}}, {"model": "contenttypes.contenttype", "pk": 7, "fields": {"app_label": "users", "model": "emailverificationcode"}}, {"model": "contenttypes.contenttype", "pk": 8, "fields": {"app_label": "users", "model": "loginlog"}}, {"model": "contenttypes.contenttype", "pk": 9, "fields": {"app_label": "forum", "model": "post"}}, {"model": "contenttypes.contenttype", "pk": 10, "fields": {"app_label": "forum", "model": "comment"}}, {"model": "contenttypes.contenttype", "pk": 11, "fields": {"app_label": "forum", "model": "postimage"}}, {"model": "contenttypes.contenttype", "pk": 12, "fields": {"app_label": "forum", "model": "postlike"}}, {"model": "contenttypes.contenttype", "pk": 13, "fields": {"app_label": "forum", "model": "commentlike"}}, {"model": "sessions.session", "pk": "rrf7n7qn9kvrnpro30pc5nbthegvoz6a", "fields": {"session_data": ".eJzNlk1zmzAQhv-Kh7NrS4AE5Jh7jj2VjGclrWw1fHT4aA8Z__dK4LQ2xYHgzLgXdtC-vLP7rCT71dtB2xx2bY3VzijvwaPe-nxNgHzBwiXUdyj25UaWRVMZsXGSzSlbb55KhdnjSXthcID6YL8Ok4TFGFLNfZ9oHwkLqA4hQKU5Up9yJBzApyzgkOhQMpQQBUmSRBFXKuyqyrFoa-v17TX1Csgx9R5WqZembcT82AbuB5F7E0BdCAlPvbVVGFtzr9VQrTR8kaaSGfbJ3FVeu_S7tuNGrsM-1VZZn9mCyk2xdZm6e257ASj19brGZk86USEoWbW5mC7qI70e16ul_R2fXRKNcjlKiD_mxSNhA9OMOUuGrHtT15zPRzDOLiv3prCPKX5_dPdhuLDvIdNgxDshgF19wtWXQCxtiIXsqo0JXYwWczDZT6yMNhIaUxbSHoIpzuMf3QX6jWCG7MN-YfcLzf7QuEVyKaAjNcRCgBt1xMRn3jkMKXdBMLJgvLq0_Lc_yrq5Ps0zzfzhLe12BNz8Dodz4hNuNnDdbQw_XHLv_CVjcti_cyCGwrtz_EjnQ6rRpHdEROJqVwxvopqZl1lQO91_wHR-30Om8eiFIcO3apdSlGVu_wRNHu432V0Yzu9zSC2ZcPucnXiCM2cznkvvzvKG_UjJP79w9BI9Oz57x9-QksaE:1uaGIW:203tN2PtykTDQavyUaCxQgSZlQGt4rSCCMb8lREjkCI", "expire_date": "2025-07-12T16:08:28.726Z"}}, {"model": "users.user", "pk": 1, "fields": {"password": "pbkdf2_sha256$1000000$ti7Z8hFbcHngRCTgs25Vg7$TSfZyJ+cYylqsnLmWRpZzCzdy3aoCbycgwqgypUcMwk=", "last_login": "2025-07-11T16:02:44.972Z", "is_superuser": true, "first_name": "", "last_name": "", "is_staff": true, "is_active": true, "date_joined": "2025-07-11T10:09:55.385Z", "email": "<EMAIL>", "username": "admin", "nickname": "", "avatar": "", "bio": "", "is_email_verified": false, "phone": "", "is_phone_verified": false, "receive_email_notifications": true, "receive_sms_notifications": false, "post_count": 0, "comment_count": 0, "like_count": 0, "created_at": "2025-07-11T10:09:56.012Z", "updated_at": "2025-07-11T10:09:56.012Z", "last_login_ip": null, "groups": [], "user_permissions": []}}]