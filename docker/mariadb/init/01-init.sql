-- 初始化数据库脚本
-- 设置字符集
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS align_forum 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE align_forum;

-- 创建用户（如果不存在）
CREATE USER IF NOT EXISTS 'align_user'@'%' IDENTIFIED BY 'align_password';

-- 授权
GRANT ALL PRIVILEGES ON align_forum.* TO 'align_user'@'%';
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, INDEX, ALTER ON align_forum.* TO 'align_user'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 显示创建的数据库
SHOW DATABASES;

-- 显示用户权限
SHOW GRANTS FOR 'align_user'@'%';
