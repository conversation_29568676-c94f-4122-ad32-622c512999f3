#!/usr/bin/env python
"""
简单的API测试脚本
用于验证API端点是否正常工作
"""

import os
import sys
import django
from django.conf import settings

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'forum_project.settings.development')

# 临时使用SQLite数据库进行测试
os.environ['USE_SQLITE'] = '1'

django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from apps.forum.models import Post, PostStatus
import json

User = get_user_model()

def test_api_endpoints():
    """测试主要API端点"""
    client = Client()
    
    print("🚀 开始测试API端点...")
    
    # 1. 测试用户列表API（未认证）
    print("\n1. 测试用户列表API（未认证）")
    response = client.get('/api/users/')
    print(f"   状态码: {response.status_code}")
    if response.status_code == 401:
        print("   ✅ 正确：需要认证才能访问用户列表")
    else:
        print(f"   ❌ 错误：期望401，实际{response.status_code}")
    
    # 2. 测试帖子列表API（未认证）
    print("\n2. 测试帖子列表API（未认证）")
    response = client.get('/api/posts/')
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        print("   ✅ 正确：未认证用户可以查看帖子列表")
        data = response.json()
        print(f"   返回数据结构: {list(data.keys()) if isinstance(data, dict) else 'list'}")
    else:
        print(f"   ❌ 错误：期望200，实际{response.status_code}")
    
    # 3. 测试发送验证码API
    print("\n3. 测试发送验证码API")
    response = client.post('/api/auth/send-code/', {
        'email': '<EMAIL>',  # 使用新邮箱避免冲突
        'code_type': 'register'
    }, content_type='application/json')
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        print("   ✅ 正确：验证码发送API正常")
    else:
        print(f"   ❌ 错误：期望200，实际{response.status_code}")
        if hasattr(response, 'json'):
            try:
                print(f"   错误信息: {response.json()}")
            except:
                print(f"   响应内容: {response.content}")
    
    # 4. 测试Token验证API（未认证）
    print("\n4. 测试Token验证API（未认证）")
    response = client.get('/api/auth/verify/')
    print(f"   状态码: {response.status_code}")
    if response.status_code == 401:
        print("   ✅ 正确：需要认证才能验证Token")
    else:
        print(f"   ❌ 错误：期望401，实际{response.status_code}")
    
    # 5. 测试API文档端点
    print("\n5. 测试API文档端点")
    response = client.get('/api/docs/')
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        print("   ✅ 正确：API文档可以正常访问")
    else:
        print(f"   ❌ 错误：期望200，实际{response.status_code}")
    
    # 6. 测试OpenAPI Schema
    print("\n6. 测试OpenAPI Schema")
    response = client.get('/api/schema/')
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        print("   ✅ 正确：OpenAPI Schema可以正常访问")
        # 检查是否是有效的YAML/JSON
        try:
            import yaml
            schema = yaml.safe_load(response.content)
            print(f"   Schema版本: {schema.get('openapi', 'unknown')}")
            print(f"   API标题: {schema.get('info', {}).get('title', 'unknown')}")
        except:
            print("   Schema格式检查失败，但端点可访问")
    else:
        print(f"   ❌ 错误：期望200，实际{response.status_code}")
    
    print("\n🎉 API端点测试完成！")

def test_with_user():
    """创建用户并测试需要认证的API"""
    print("\n🔐 测试需要认证的API...")
    
    # 创建测试用户
    try:
        # 先删除可能存在的用户
        User.objects.filter(email='<EMAIL>').delete()
        User.objects.filter(username='testuser').delete()

        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        print("   ✅ 测试用户创建成功")
    except Exception as e:
        print(f"   ❌ 创建用户失败: {e}")
        return
    
    client = Client()
    
    # 登录获取Token
    response = client.post('/api/auth/login/', {
        'email': '<EMAIL>',
        'password': 'testpass123'
    }, content_type='application/json')
    
    if response.status_code == 200:
        print("   ✅ 用户登录成功")
        data = response.json()
        token = data.get('access')
        
        # 使用Token访问需要认证的API
        headers = {'HTTP_AUTHORIZATION': f'Bearer {token}'}
        
        # 测试获取当前用户信息
        response = client.get('/api/users/me/', **headers)
        if response.status_code == 200:
            print("   ✅ 获取当前用户信息成功")
            user_data = response.json()
            print(f"   用户信息: {user_data.get('username')} ({user_data.get('email')})")
        else:
            print(f"   ❌ 获取用户信息失败: {response.status_code}")
        
        # 测试创建帖子
        response = client.post('/api/posts/', {
            'title': '测试帖子',
            'content': '这是一个测试帖子的内容',
            'status': 'published'
        }, content_type='application/json', **headers)
        
        if response.status_code == 201:
            print("   ✅ 创建帖子成功")
            post_data = response.json()
            print(f"   帖子ID: {post_data.get('id')}")
        else:
            print(f"   ❌ 创建帖子失败: {response.status_code}")
            try:
                print(f"   错误信息: {response.json()}")
            except:
                print(f"   响应内容: {response.content}")
    
    else:
        print(f"   ❌ 用户登录失败: {response.status_code}")
        try:
            print(f"   错误信息: {response.json()}")
        except:
            print(f"   响应内容: {response.content}")
    
    # 清理测试数据
    try:
        user.delete()
        print("   🧹 测试用户已清理")
    except:
        pass

if __name__ == '__main__':
    test_api_endpoints()
    test_with_user()
    print("\n✨ 所有测试完成！")
