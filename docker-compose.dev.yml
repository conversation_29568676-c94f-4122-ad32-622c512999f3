version: '3.8'

services:
  mariadb:
    image: mariadb:10.11
    container_name: align_mariadb_dev
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: align_forum
      MYSQL_USER: align_user
      MYSQL_PASSWORD: align_password
      MYSQL_CHARACTER_SET_SERVER: utf8mb4
      MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - ./docker/mariadb/data:/var/lib/mysql
      - ./docker/mariadb/init:/docker-entrypoint-initdb.d
      - ./docker/mariadb/conf:/etc/mysql/conf.d
    networks:
      - align_network

networks:
  align_network:
    driver: bridge
