openapi: 3.0.3
info:
  title: Align Forum API
  version: 1.0.0
  description: 论坛系统后端API文档
paths:
  /api/auth/login/:
    post:
      operationId: auth_login_create
      description: 使用邮箱和密码进行登录，返回JWT Token
      summary: 邮箱密码登录
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomTokenObtainPairRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CustomTokenObtainPairRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CustomTokenObtainPairRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  refresh:
                    type: string
                    description: 刷新Token
                  access:
                    type: string
                    description: 访问Token
                  user:
                    type: object
                    properties:
                      id:
                        type: integer
                      email:
                        type: string
                      username:
                        type: string
                      nickname:
                        type: string
                      avatar_url:
                        type: string
          description: ''
        '400':
          content:
            application/json:
              schema:
                description: 登录失败
          description: ''
  /api/auth/login/email-code/:
    post:
      operationId: auth_login_email_code_create
      description: 使用邮箱验证码进行登录
      summary: 邮箱验证码登录
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmailCodeLoginRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/EmailCodeLoginRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/EmailCodeLoginRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  refresh:
                    type: string
                    description: 刷新Token
                  access:
                    type: string
                    description: 访问Token
                  user:
                    type: object
                    properties:
                      id:
                        type: integer
                      email:
                        type: string
                      username:
                        type: string
                      nickname:
                        type: string
                      avatar_url:
                        type: string
          description: ''
        '400':
          content:
            application/json:
              schema:
                description: 验证码错误或已过期
          description: ''
  /api/auth/logout/:
    post:
      operationId: auth_logout_create
      description: 登出当前用户，将刷新Token加入黑名单
      summary: 用户登出
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LogoutRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/LogoutRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/LogoutRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                description: 登出成功
          description: ''
        '400':
          content:
            application/json:
              schema:
                description: Token无效
          description: ''
  /api/auth/refresh/:
    post:
      operationId: auth_refresh_create
      description: |-
        Takes a refresh type JSON web token and returns an access type JSON web
        token if the refresh token is valid.
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TokenRefreshRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/TokenRefreshRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TokenRefreshRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenRefresh'
          description: ''
  /api/auth/send-code/:
    post:
      operationId: auth_send_code_create
      description: 向指定邮箱发送验证码，支持注册、登录、重置密码等场景
      summary: 发送邮箱验证码
      tags:
      - auth
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendVerificationCodeRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/SendVerificationCodeRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/SendVerificationCodeRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                description: 验证码发送成功
          description: ''
        '400':
          content:
            application/json:
              schema:
                description: 请求参数错误
          description: ''
  /api/auth/verify/:
    get:
      operationId: auth_verify_retrieve
      description: 验证当前用户的Token是否有效
      summary: 验证Token有效性
      tags:
      - auth
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  valid:
                    type: boolean
                    description: Token是否有效
                  user:
                    type: object
                    properties:
                      id:
                        type: integer
                      email:
                        type: string
                      username:
                        type: string
                      nickname:
                        type: string
          description: ''
        '401':
          content:
            application/json:
              schema:
                description: Token无效或已过期
          description: ''
  /api/comments/:
    get:
      operationId: comments_list
      description: 获取评论列表（管理员功能）
      summary: 获取评论列表
      parameters:
      - in: query
        name: author
        schema:
          type: integer
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - in: query
        name: parent
        schema:
          type: string
          format: uuid
      - in: query
        name: post
        schema:
          type: string
          format: uuid
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      - in: query
        name: status
        schema:
          type: string
          title: 状态
          enum:
          - deleted
          - hidden
          - pending
          - published
        description: |-
          * `published` - 已发布
          * `pending` - 待审核
          * `hidden` - 已隐藏
          * `deleted` - 已删除
      tags:
      - comments
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedCommentList'
          description: ''
    post:
      operationId: comments_create
      description: 评论管理视图集
      tags:
      - comments
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CommentRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CommentRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CommentRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Comment'
          description: ''
  /api/comments/{id}/:
    get:
      operationId: comments_retrieve
      description: 获取指定评论的详细信息
      summary: 获取评论详情
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 评论.
        required: true
      tags:
      - comments
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Comment'
          description: ''
    put:
      operationId: comments_update
      description: 更新评论内容（仅作者或管理员）
      summary: 更新评论
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 评论.
        required: true
      tags:
      - comments
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CommentUpdateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CommentUpdateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CommentUpdateRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommentUpdate'
          description: ''
    patch:
      operationId: comments_partial_update
      description: 部分更新评论内容（仅作者或管理员）
      summary: 部分更新评论
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 评论.
        required: true
      tags:
      - comments
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedCommentUpdateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedCommentUpdateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedCommentUpdateRequest'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CommentUpdate'
          description: ''
    delete:
      operationId: comments_destroy
      description: 删除评论（仅作者或管理员）
      summary: 删除评论
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 评论.
        required: true
      tags:
      - comments
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/comments/{id}/like/:
    post:
      operationId: comments_like_create
      description: 对评论进行点赞或取消点赞操作
      summary: 点赞/取消点赞评论
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 评论.
        required: true
      tags:
      - comments
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CommentRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CommentRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CommentRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  liked:
                    type: boolean
                    description: 是否已点赞
                  like_count:
                    type: integer
                    description: 总点赞数
          description: ''
  /api/comments/{id}/replies/:
    get:
      operationId: comments_replies_retrieve
      description: 获取指定评论的回复列表
      summary: 获取评论回复
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 评论.
        required: true
      tags:
      - comments
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Comment'
          description: ''
  /api/login-logs/:
    get:
      operationId: login_logs_list
      description: 获取用户登录日志记录
      summary: 获取登录日志
      parameters:
      - in: query
        name: email
        schema:
          type: string
      - in: query
        name: login_type
        schema:
          type: string
          enum:
          - email_code
          - password
          - phone_code
          - social
        description: 登录方式过滤
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - in: query
        name: status
        schema:
          type: string
          enum:
          - blocked
          - failed
          - success
        description: 登录状态过滤
      tags:
      - users
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedLoginLogList'
          description: ''
  /api/login-logs/{id}/:
    get:
      operationId: login_logs_retrieve
      description: 登录日志视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 登录日志.
        required: true
      tags:
      - login-logs
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginLog'
          description: ''
  /api/posts/:
    get:
      operationId: posts_list
      description: 获取论坛帖子列表，支持搜索、过滤和排序
      summary: 获取帖子列表
      parameters:
      - in: query
        name: author
        schema:
          type: integer
        description: 按作者ID过滤
      - in: query
        name: is_featured
        schema:
          type: boolean
        description: 是否精选
      - in: query
        name: is_pinned
        schema:
          type: boolean
        description: 是否置顶
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - in: query
        name: search
        schema:
          type: string
        description: 搜索帖子标题和内容
      - in: query
        name: status
        schema:
          type: string
          enum:
          - deleted
          - draft
          - hidden
          - published
        description: 帖子状态过滤
      tags:
      - posts
      security:
      - jwtAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedPostListList'
          description: ''
    post:
      operationId: posts_create
      description: 创建新的论坛帖子
      summary: 创建帖子
      tags:
      - posts
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostCreateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PostCreateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PostCreateRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostCreate'
          description: ''
  /api/posts/{id}/:
    get:
      operationId: posts_retrieve
      description: 根据帖子ID获取详细信息，会自动增加浏览次数
      summary: 获取帖子详情
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 帖子.
        required: true
      tags:
      - posts
      security:
      - jwtAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostDetail'
          description: ''
    put:
      operationId: posts_update
      description: 更新帖子信息（仅作者或管理员）
      summary: 更新帖子
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 帖子.
        required: true
      tags:
      - posts
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostUpdateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PostUpdateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PostUpdateRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostUpdate'
          description: ''
    patch:
      operationId: posts_partial_update
      description: 部分更新帖子信息（仅作者或管理员）
      summary: 部分更新帖子
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 帖子.
        required: true
      tags:
      - posts
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedPostUpdateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedPostUpdateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedPostUpdateRequest'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostUpdate'
          description: ''
    delete:
      operationId: posts_destroy
      description: 删除帖子（仅作者或管理员）
      summary: 删除帖子
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 帖子.
        required: true
      tags:
      - posts
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/posts/{id}/comments/:
    get:
      operationId: posts_comments_retrieve
      description: 获取指定帖子的评论列表
      summary: 获取帖子评论
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 帖子.
        required: true
      - in: query
        name: ordering
        schema:
          type: string
          enum:
          - -created_at
          - -like_count
          - created_at
          - like_count
        description: 排序方式
      tags:
      - posts
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostDetail'
          description: ''
  /api/posts/{id}/create_comment/:
    post:
      operationId: posts_create_comment_create
      description: 为指定帖子创建评论
      summary: 创建帖子评论
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 帖子.
        required: true
      tags:
      - posts
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CommentCreateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CommentCreateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CommentCreateRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Comment'
          description: ''
  /api/posts/{id}/like/:
    post:
      operationId: posts_like_create
      description: 对帖子进行点赞或取消点赞操作
      summary: 点赞/取消点赞帖子
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 帖子.
        required: true
      tags:
      - posts
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostDetailRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PostDetailRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PostDetailRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  liked:
                    type: boolean
                    description: 是否已点赞
                  like_count:
                    type: integer
                    description: 总点赞数
          description: ''
  /api/posts/stats/:
    get:
      operationId: posts_stats_retrieve
      description: 获取论坛的整体统计信息
      summary: 获取帖子统计信息
      tags:
      - posts
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostStats'
          description: ''
  /api/users/:
    get:
      operationId: users_list
      description: 获取系统中的用户列表，支持搜索和过滤
      summary: 获取用户列表
      parameters:
      - in: query
        name: is_active
        schema:
          type: boolean
        description: 过滤活跃用户
      - in: query
        name: is_email_verified
        schema:
          type: boolean
      - in: query
        name: is_phone_verified
        schema:
          type: boolean
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - in: query
        name: search
        schema:
          type: string
        description: 搜索用户名、邮箱或昵称
      tags:
      - users
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedUserList'
          description: ''
    post:
      operationId: users_create
      description: 注册新用户账户
      summary: 创建用户
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserCreateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserCreateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserCreateRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      - {}
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserCreate'
          description: ''
  /api/users/{id}/:
    get:
      operationId: users_retrieve
      description: 根据用户ID获取用户详细信息
      summary: 获取用户详情
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this 用户.
        required: true
      tags:
      - users
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserDetail'
          description: ''
    put:
      operationId: users_update
      description: 更新用户的基本信息
      summary: 更新用户信息
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this 用户.
        required: true
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserUpdateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserUpdateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserUpdateRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserUpdate'
          description: ''
    patch:
      operationId: users_partial_update
      description: 部分更新用户的基本信息
      summary: 部分更新用户信息
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this 用户.
        required: true
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUserUpdateRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUserUpdateRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUserUpdateRequest'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserUpdate'
          description: ''
    delete:
      operationId: users_destroy
      description: 删除用户账户（软删除）
      summary: 删除用户
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this 用户.
        required: true
      tags:
      - users
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/users/{id}/change_password/:
    post:
      operationId: users_change_password_create
      description: 修改当前用户的密码
      summary: 修改密码
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this 用户.
        required: true
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangePasswordRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ChangePasswordRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ChangePasswordRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                description: 密码修改成功
          description: ''
  /api/users/{id}/posts/:
    get:
      operationId: users_posts_retrieve
      description: 获取指定用户发布的帖子列表
      summary: 获取用户的帖子
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this 用户.
        required: true
      - in: query
        name: status
        schema:
          type: string
          enum:
          - deleted
          - draft
          - hidden
          - published
        description: 帖子状态过滤
      tags:
      - users
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserDetail'
          description: ''
  /api/users/{id}/stats/:
    get:
      operationId: users_stats_retrieve
      description: 获取用户的发帖、评论、点赞等统计信息
      summary: 获取用户统计信息
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this 用户.
        required: true
      tags:
      - users
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  post_count:
                    type: integer
                    description: 发帖数
                  comment_count:
                    type: integer
                    description: 评论数
                  like_count:
                    type: integer
                    description: 获赞数
                  login_count:
                    type: integer
                    description: 登录次数
                  last_login:
                    type: string
                    format: date-time
                    description: 最后登录时间
          description: ''
  /api/verification-codes/:
    get:
      operationId: verification_codes_list
      description: 获取邮箱验证码记录（管理员功能）
      summary: 获取邮箱验证码列表
      parameters:
      - in: query
        name: code_type
        schema:
          type: string
          title: 验证码类型
          enum:
          - change_email
          - login
          - register
          - reset_password
        description: |-
          * `register` - 注册验证
          * `login` - 登录验证
          * `reset_password` - 重置密码
          * `change_email` - 更换邮箱
      - in: query
        name: email
        schema:
          type: string
      - in: query
        name: is_used
        schema:
          type: boolean
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - users
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedEmailVerificationCodeList'
          description: ''
    post:
      operationId: verification_codes_create
      description: 向指定邮箱发送验证码
      summary: 发送邮箱验证码
      tags:
      - users
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmailVerificationCodeRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/EmailVerificationCodeRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/EmailVerificationCodeRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      - {}
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailVerificationCode'
          description: ''
  /api/verification-codes/{id}/:
    get:
      operationId: verification_codes_retrieve
      description: 邮箱验证码管理视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 邮箱验证码.
        required: true
      tags:
      - verification-codes
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailVerificationCode'
          description: ''
    put:
      operationId: verification_codes_update
      description: 邮箱验证码管理视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 邮箱验证码.
        required: true
      tags:
      - verification-codes
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmailVerificationCodeRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/EmailVerificationCodeRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/EmailVerificationCodeRequest'
        required: true
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailVerificationCode'
          description: ''
    patch:
      operationId: verification_codes_partial_update
      description: 邮箱验证码管理视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 邮箱验证码.
        required: true
      tags:
      - verification-codes
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedEmailVerificationCodeRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedEmailVerificationCodeRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedEmailVerificationCodeRequest'
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailVerificationCode'
          description: ''
    delete:
      operationId: verification_codes_destroy
      description: 邮箱验证码管理视图集
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this 邮箱验证码.
        required: true
      tags:
      - verification-codes
      security:
      - jwtAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
components:
  schemas:
    ChangePasswordRequest:
      type: object
      description: 修改密码序列化器
      properties:
        old_password:
          type: string
          minLength: 1
        new_password:
          type: string
          minLength: 1
        new_password_confirm:
          type: string
          minLength: 1
      required:
      - new_password
      - new_password_confirm
      - old_password
    CodeTypeEnum:
      enum:
      - register
      - login
      - reset_password
      - change_email
      type: string
      description: |-
        * `register` - 注册验证
        * `login` - 登录验证
        * `reset_password` - 重置密码
        * `change_email` - 更换邮箱
    Comment:
      type: object
      description: 评论序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        content:
          type: string
          title: 评论内容
          maxLength: 1000
        author:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        parent:
          type: string
          format: uuid
          nullable: true
          title: 父评论
        reply_to:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        status:
          allOf:
          - $ref: '#/components/schemas/CommentStatusEnum'
          title: 状态
        like_count:
          type: integer
          readOnly: true
          title: 点赞数
        reply_count:
          type: integer
          readOnly: true
          title: 回复数
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
        replies:
          type: string
          readOnly: true
        is_liked:
          type: string
          readOnly: true
      required:
      - author
      - content
      - created_at
      - id
      - is_liked
      - like_count
      - replies
      - reply_count
      - reply_to
      - updated_at
    CommentCreateRequest:
      type: object
      description: 创建评论序列化器
      properties:
        content:
          type: string
          minLength: 1
          title: 评论内容
          maxLength: 1000
        parent:
          type: string
          format: uuid
          nullable: true
          title: 父评论
        reply_to:
          type: integer
          nullable: true
          title: 回复给
      required:
      - content
    CommentRequest:
      type: object
      description: 评论序列化器
      properties:
        content:
          type: string
          minLength: 1
          title: 评论内容
          maxLength: 1000
        parent:
          type: string
          format: uuid
          nullable: true
          title: 父评论
        status:
          allOf:
          - $ref: '#/components/schemas/CommentStatusEnum'
          title: 状态
      required:
      - content
    CommentStatusEnum:
      enum:
      - published
      - pending
      - hidden
      - deleted
      type: string
      description: |-
        * `published` - 已发布
        * `pending` - 待审核
        * `hidden` - 已隐藏
        * `deleted` - 已删除
    CommentUpdate:
      type: object
      description: 更新评论序列化器
      properties:
        content:
          type: string
          title: 评论内容
          maxLength: 1000
      required:
      - content
    CommentUpdateRequest:
      type: object
      description: 更新评论序列化器
      properties:
        content:
          type: string
          minLength: 1
          title: 评论内容
          maxLength: 1000
      required:
      - content
    CustomTokenObtainPairRequest:
      type: object
      description: 自定义JWT Token获取序列化器
      properties:
        email:
          type: string
          writeOnly: true
          minLength: 1
        password:
          type: string
          writeOnly: true
          minLength: 1
      required:
      - email
      - password
    EmailCodeLoginRequest:
      type: object
      description: 邮箱验证码登录序列化器
      properties:
        email:
          type: string
          format: email
          minLength: 1
        code:
          type: string
          minLength: 1
          maxLength: 6
      required:
      - code
      - email
    EmailVerificationCode:
      type: object
      description: 邮箱验证码序列化器
      properties:
        email:
          type: string
          format: email
          title: 邮箱地址
          maxLength: 254
        code_type:
          allOf:
          - $ref: '#/components/schemas/CodeTypeEnum'
          title: 验证码类型
      required:
      - code_type
      - email
    EmailVerificationCodeRequest:
      type: object
      description: 邮箱验证码序列化器
      properties:
        email:
          type: string
          format: email
          minLength: 1
          title: 邮箱地址
          maxLength: 254
        code_type:
          allOf:
          - $ref: '#/components/schemas/CodeTypeEnum'
          title: 验证码类型
      required:
      - code_type
      - email
    LoginLog:
      type: object
      description: 登录日志序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        user:
          type: integer
          nullable: true
          title: 用户
        user_display:
          type: string
          readOnly: true
        email:
          type: string
          format: email
          title: 登录邮箱
          maxLength: 254
        login_type:
          allOf:
          - $ref: '#/components/schemas/LoginTypeEnum'
          title: 登录方式
        status:
          allOf:
          - $ref: '#/components/schemas/LoginLogStatusEnum'
          title: 登录状态
        ip_address:
          type: string
          title: IP地址
        location:
          type: string
          title: 登录地点
          maxLength: 100
        failure_reason:
          type: string
          title: 失败原因
          maxLength: 200
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 登录时间
      required:
      - created_at
      - email
      - id
      - ip_address
      - login_type
      - status
      - user_display
    LoginLogStatusEnum:
      enum:
      - success
      - failed
      - blocked
      type: string
      description: |-
        * `success` - 成功
        * `failed` - 失败
        * `blocked` - 被阻止
    LoginTypeEnum:
      enum:
      - password
      - email_code
      - phone_code
      - social
      type: string
      description: |-
        * `password` - 密码登录
        * `email_code` - 邮箱验证码登录
        * `phone_code` - 手机验证码登录
        * `social` - 第三方登录
    LogoutRequest:
      type: object
      description: 登出序列化器
      properties:
        refresh:
          type: string
          minLength: 1
      required:
      - refresh
    PaginatedCommentList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Comment'
    PaginatedEmailVerificationCodeList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/EmailVerificationCode'
    PaginatedLoginLogList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/LoginLog'
    PaginatedPostListList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/PostList'
    PaginatedUserList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/User'
    PatchedCommentUpdateRequest:
      type: object
      description: 更新评论序列化器
      properties:
        content:
          type: string
          minLength: 1
          title: 评论内容
          maxLength: 1000
    PatchedEmailVerificationCodeRequest:
      type: object
      description: 邮箱验证码序列化器
      properties:
        email:
          type: string
          format: email
          minLength: 1
          title: 邮箱地址
          maxLength: 254
        code_type:
          allOf:
          - $ref: '#/components/schemas/CodeTypeEnum'
          title: 验证码类型
    PatchedPostUpdateRequest:
      type: object
      description: 更新帖子序列化器
      properties:
        title:
          type: string
          minLength: 1
          title: 标题
          maxLength: 200
        content:
          type: string
          minLength: 1
          title: 正文内容
          description: 支持Markdown格式
        status:
          allOf:
          - $ref: '#/components/schemas/StatusF81Enum'
          title: 状态
        allow_comments:
          type: boolean
          title: 允许评论
        meta_description:
          type: string
          title: SEO描述
          maxLength: 160
        images:
          type: array
          items:
            $ref: '#/components/schemas/PostImageRequest'
    PatchedUserUpdateRequest:
      type: object
      description: 用户信息更新序列化器
      properties:
        username:
          type: string
          minLength: 1
          title: 用户名
          maxLength: 150
        nickname:
          type: string
          title: 昵称
          maxLength: 50
        avatar:
          type: string
          format: binary
          nullable: true
          title: 头像
        bio:
          type: string
          title: 个人简介
          maxLength: 500
        phone:
          type: string
          title: 手机号
          maxLength: 20
        receive_email_notifications:
          type: boolean
          title: 接收邮件通知
        receive_sms_notifications:
          type: boolean
          title: 接收短信通知
    PostCreate:
      type: object
      description: 创建帖子序列化器
      properties:
        title:
          type: string
          title: 标题
          maxLength: 200
        content:
          type: string
          title: 正文内容
          description: 支持Markdown格式
        status:
          allOf:
          - $ref: '#/components/schemas/StatusF81Enum'
          title: 状态
        is_pinned:
          type: boolean
          title: 置顶
        is_featured:
          type: boolean
          title: 精选
        allow_comments:
          type: boolean
          title: 允许评论
        meta_description:
          type: string
          title: SEO描述
          maxLength: 160
        images:
          type: array
          items:
            $ref: '#/components/schemas/PostImage'
      required:
      - content
      - title
    PostCreateRequest:
      type: object
      description: 创建帖子序列化器
      properties:
        title:
          type: string
          minLength: 1
          title: 标题
          maxLength: 200
        content:
          type: string
          minLength: 1
          title: 正文内容
          description: 支持Markdown格式
        status:
          allOf:
          - $ref: '#/components/schemas/StatusF81Enum'
          title: 状态
        is_pinned:
          type: boolean
          title: 置顶
        is_featured:
          type: boolean
          title: 精选
        allow_comments:
          type: boolean
          title: 允许评论
        meta_description:
          type: string
          title: SEO描述
          maxLength: 160
        images:
          type: array
          items:
            $ref: '#/components/schemas/PostImageRequest'
      required:
      - content
      - title
    PostDetail:
      type: object
      description: 帖子详情序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        title:
          type: string
          title: 标题
          maxLength: 200
        content:
          type: string
          title: 正文内容
          description: 支持Markdown格式
        excerpt:
          type: string
          title: 摘要
          description: 自动从内容中提取
          maxLength: 500
        author:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        status:
          allOf:
          - $ref: '#/components/schemas/StatusF81Enum'
          title: 状态
        view_count:
          type: integer
          readOnly: true
          title: 浏览次数
        like_count:
          type: integer
          readOnly: true
          title: 点赞数
        comment_count:
          type: integer
          readOnly: true
          title: 评论数
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
        published_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          title: 发布时间
        slug:
          type: string
          readOnly: true
          title: URL别名
          pattern: ^[-a-zA-Z0-9_]+$
        is_pinned:
          type: boolean
          title: 置顶
        is_featured:
          type: boolean
          title: 精选
        allow_comments:
          type: boolean
          title: 允许评论
        images:
          type: array
          items:
            $ref: '#/components/schemas/PostImage'
          readOnly: true
        is_liked:
          type: string
          readOnly: true
        meta_description:
          type: string
          title: SEO描述
          maxLength: 160
      required:
      - author
      - comment_count
      - content
      - created_at
      - id
      - images
      - is_liked
      - like_count
      - published_at
      - slug
      - title
      - updated_at
      - view_count
    PostDetailRequest:
      type: object
      description: 帖子详情序列化器
      properties:
        title:
          type: string
          minLength: 1
          title: 标题
          maxLength: 200
        content:
          type: string
          minLength: 1
          title: 正文内容
          description: 支持Markdown格式
        excerpt:
          type: string
          title: 摘要
          description: 自动从内容中提取
          maxLength: 500
        status:
          allOf:
          - $ref: '#/components/schemas/StatusF81Enum'
          title: 状态
        is_pinned:
          type: boolean
          title: 置顶
        is_featured:
          type: boolean
          title: 精选
        allow_comments:
          type: boolean
          title: 允许评论
        meta_description:
          type: string
          title: SEO描述
          maxLength: 160
      required:
      - content
      - title
    PostImage:
      type: object
      description: 帖子图片序列化器
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        image:
          type: string
          format: uri
          title: 图片
        caption:
          type: string
          title: 图片说明
          maxLength: 200
        order:
          type: integer
          maximum: 4294967295
          minimum: 0
          format: int64
          title: 排序
      required:
      - id
      - image
    PostImageRequest:
      type: object
      description: 帖子图片序列化器
      properties:
        image:
          type: string
          format: binary
          title: 图片
        caption:
          type: string
          title: 图片说明
          maxLength: 200
        order:
          type: integer
          maximum: 4294967295
          minimum: 0
          format: int64
          title: 排序
      required:
      - image
    PostList:
      type: object
      description: 帖子列表序列化器（简化版）
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        title:
          type: string
          title: 标题
          maxLength: 200
        excerpt:
          type: string
          title: 摘要
          description: 自动从内容中提取
          maxLength: 500
        author:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        status:
          allOf:
          - $ref: '#/components/schemas/StatusF81Enum'
          title: 状态
        view_count:
          type: integer
          readOnly: true
          title: 浏览次数
        like_count:
          type: integer
          readOnly: true
          title: 点赞数
        comment_count:
          type: integer
          readOnly: true
          title: 评论数
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 创建时间
        published_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          title: 发布时间
        is_pinned:
          type: boolean
          title: 置顶
        is_featured:
          type: boolean
          title: 精选
        is_liked:
          type: string
          readOnly: true
      required:
      - author
      - comment_count
      - created_at
      - id
      - is_liked
      - like_count
      - published_at
      - title
      - view_count
    PostStats:
      type: object
      description: 帖子统计信息序列化器
      properties:
        total_posts:
          type: integer
        published_posts:
          type: integer
        draft_posts:
          type: integer
        total_views:
          type: integer
        total_likes:
          type: integer
        total_comments:
          type: integer
      required:
      - draft_posts
      - published_posts
      - total_comments
      - total_likes
      - total_posts
      - total_views
    PostUpdate:
      type: object
      description: 更新帖子序列化器
      properties:
        title:
          type: string
          title: 标题
          maxLength: 200
        content:
          type: string
          title: 正文内容
          description: 支持Markdown格式
        status:
          allOf:
          - $ref: '#/components/schemas/StatusF81Enum'
          title: 状态
        allow_comments:
          type: boolean
          title: 允许评论
        meta_description:
          type: string
          title: SEO描述
          maxLength: 160
        images:
          type: array
          items:
            $ref: '#/components/schemas/PostImage'
      required:
      - content
      - title
    PostUpdateRequest:
      type: object
      description: 更新帖子序列化器
      properties:
        title:
          type: string
          minLength: 1
          title: 标题
          maxLength: 200
        content:
          type: string
          minLength: 1
          title: 正文内容
          description: 支持Markdown格式
        status:
          allOf:
          - $ref: '#/components/schemas/StatusF81Enum'
          title: 状态
        allow_comments:
          type: boolean
          title: 允许评论
        meta_description:
          type: string
          title: SEO描述
          maxLength: 160
        images:
          type: array
          items:
            $ref: '#/components/schemas/PostImageRequest'
      required:
      - content
      - title
    SendVerificationCodeRequest:
      type: object
      description: 发送验证码序列化器
      properties:
        email:
          type: string
          format: email
          minLength: 1
        code_type:
          $ref: '#/components/schemas/CodeTypeEnum'
      required:
      - code_type
      - email
    StatusF81Enum:
      enum:
      - draft
      - published
      - hidden
      - deleted
      type: string
      description: |-
        * `draft` - 草稿
        * `published` - 已发布
        * `hidden` - 已隐藏
        * `deleted` - 已删除
    TokenRefresh:
      type: object
      properties:
        access:
          type: string
          readOnly: true
        refresh:
          type: string
      required:
      - access
      - refresh
    TokenRefreshRequest:
      type: object
      properties:
        refresh:
          type: string
          minLength: 1
      required:
      - refresh
    User:
      type: object
      description: 用户基本信息序列化器
      properties:
        id:
          type: integer
          readOnly: true
        username:
          type: string
          title: 用户名
          maxLength: 150
        email:
          type: string
          format: email
          readOnly: true
          title: 邮箱地址
        nickname:
          type: string
          title: 昵称
          maxLength: 50
        avatar:
          type: string
          format: uri
          nullable: true
          title: 头像
        avatar_url:
          type: string
          readOnly: true
        bio:
          type: string
          title: 个人简介
          maxLength: 500
        display_name:
          type: string
          readOnly: true
        is_email_verified:
          type: boolean
          readOnly: true
          title: 邮箱已验证
        is_phone_verified:
          type: boolean
          readOnly: true
          title: 手机已验证
        post_count:
          type: integer
          readOnly: true
          title: 发帖数
        comment_count:
          type: integer
          readOnly: true
          title: 评论数
        like_count:
          type: integer
          readOnly: true
          title: 获赞数
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 注册时间
        last_login:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          title: 上次登录
      required:
      - avatar_url
      - comment_count
      - created_at
      - display_name
      - email
      - id
      - is_email_verified
      - is_phone_verified
      - last_login
      - like_count
      - post_count
      - username
    UserCreate:
      type: object
      description: 用户注册序列化器
      properties:
        username:
          type: string
          title: 用户名
          maxLength: 150
        email:
          type: string
          format: email
          title: 邮箱地址
          maxLength: 254
        nickname:
          type: string
          title: 昵称
          maxLength: 50
      required:
      - email
      - username
    UserCreateRequest:
      type: object
      description: 用户注册序列化器
      properties:
        username:
          type: string
          minLength: 1
          title: 用户名
          maxLength: 150
        email:
          type: string
          format: email
          minLength: 1
          title: 邮箱地址
          maxLength: 254
        password:
          type: string
          writeOnly: true
          minLength: 1
        password_confirm:
          type: string
          writeOnly: true
          minLength: 1
        nickname:
          type: string
          title: 昵称
          maxLength: 50
      required:
      - email
      - password
      - password_confirm
      - username
    UserDetail:
      type: object
      description: 用户详细信息序列化器
      properties:
        id:
          type: integer
          readOnly: true
        username:
          type: string
          title: 用户名
          maxLength: 150
        email:
          type: string
          format: email
          readOnly: true
          title: 邮箱地址
        nickname:
          type: string
          title: 昵称
          maxLength: 50
        avatar:
          type: string
          format: uri
          nullable: true
          title: 头像
        avatar_url:
          type: string
          readOnly: true
        bio:
          type: string
          title: 个人简介
          maxLength: 500
        display_name:
          type: string
          readOnly: true
        is_email_verified:
          type: boolean
          readOnly: true
          title: 邮箱已验证
        is_phone_verified:
          type: boolean
          readOnly: true
          title: 手机已验证
        post_count:
          type: integer
          readOnly: true
          title: 发帖数
        comment_count:
          type: integer
          readOnly: true
          title: 评论数
        like_count:
          type: integer
          readOnly: true
          title: 获赞数
        created_at:
          type: string
          format: date-time
          readOnly: true
          title: 注册时间
        last_login:
          type: string
          format: date-time
          readOnly: true
          nullable: true
          title: 上次登录
        phone:
          type: string
          title: 手机号
          maxLength: 20
        receive_email_notifications:
          type: boolean
          title: 接收邮件通知
        receive_sms_notifications:
          type: boolean
          title: 接收短信通知
        updated_at:
          type: string
          format: date-time
          readOnly: true
          title: 更新时间
      required:
      - avatar_url
      - comment_count
      - created_at
      - display_name
      - email
      - id
      - is_email_verified
      - is_phone_verified
      - last_login
      - like_count
      - post_count
      - updated_at
      - username
    UserRequest:
      type: object
      description: 用户基本信息序列化器
      properties:
        username:
          type: string
          minLength: 1
          title: 用户名
          maxLength: 150
        nickname:
          type: string
          title: 昵称
          maxLength: 50
        avatar:
          type: string
          format: binary
          nullable: true
          title: 头像
        bio:
          type: string
          title: 个人简介
          maxLength: 500
      required:
      - username
    UserUpdate:
      type: object
      description: 用户信息更新序列化器
      properties:
        username:
          type: string
          title: 用户名
          maxLength: 150
        nickname:
          type: string
          title: 昵称
          maxLength: 50
        avatar:
          type: string
          format: uri
          nullable: true
          title: 头像
        bio:
          type: string
          title: 个人简介
          maxLength: 500
        phone:
          type: string
          title: 手机号
          maxLength: 20
        receive_email_notifications:
          type: boolean
          title: 接收邮件通知
        receive_sms_notifications:
          type: boolean
          title: 接收短信通知
      required:
      - username
    UserUpdateRequest:
      type: object
      description: 用户信息更新序列化器
      properties:
        username:
          type: string
          minLength: 1
          title: 用户名
          maxLength: 150
        nickname:
          type: string
          title: 昵称
          maxLength: 50
        avatar:
          type: string
          format: binary
          nullable: true
          title: 头像
        bio:
          type: string
          title: 个人简介
          maxLength: 500
        phone:
          type: string
          title: 手机号
          maxLength: 20
        receive_email_notifications:
          type: boolean
          title: 接收邮件通知
        receive_sms_notifications:
          type: boolean
          title: 接收短信通知
      required:
      - username
  securitySchemes:
    cookieAuth:
      type: apiKey
      in: cookie
      name: sessionid
    jwtAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
servers:
- url: http://localhost:8000
  description: 开发环境
- url: https://api.align-forum.com
  description: 生产环境
tags:
- name: auth
  description: 用户认证相关接口
- name: users
  description: 用户管理相关接口
- name: posts
  description: 帖子相关接口
- name: comments
  description: 评论相关接口
