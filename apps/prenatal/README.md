# 产检管理模块 (Prenatal App)

## 概述

产检管理模块是Align Backend论坛系统的一个重要组成部分，专门为孕妇用户提供产检记录管理功能。该模块包含三个核心数据模型，支持标准产检项目管理、自定义产检项目创建以及完整的产检记录跟踪。

## 功能特性

- **标准产检项目管理**: 预置15种常见产检项目，按孕周分类
- **自定义产检项目**: 用户可创建个性化产检项目
- **产检记录管理**: 完整的产检预约、记录和跟踪功能
- **图片存储**: 支持产检单、B超单等图片记录
- **时间提醒**: 支持出行时间和提醒时间设置
- **状态管理**: 产检状态跟踪（已预约、已完成、已取消、已改期）

## 数据模型

### 1. PrenatalItem (标准产检项目表)

存储系统预置的标准产检项目。

**字段说明:**
- `id`: UUID主键
- `name`: 项目名称（必填）
- `content`: 项目详细内容（可选）
- `start_week`: 适用开始孕周（1-42周，可选）
- `end_week`: 适用结束孕周（1-42周，可选）
- `created_at`: 创建时间
- `updated_at`: 更新时间

**预置项目包括:**
- 建档检查 (6-12周)
- NT检查 (11-13周)
- 早期/中期唐氏筛查
- 四维彩超 (20-24周)
- 糖耐量试验 (24-28周)
- 胎心监护 (32-42周)
- 血常规、尿常规等常规检查

### 2. CustomPrenatalItem (自定义产检项目表)

继承自PrenatalItem，用户可创建个性化产检项目。

**额外字段:**
- `created_by`: 创建者用户ID（外键关联UserAccount）

### 3. PrenatalCheckup (产检记录表)

存储用户的产检预约和记录信息。

**字段说明:**
- `id`: UUID主键
- `user`: 用户ID（外键关联UserAccount）
- `datetime`: 产检日期和时间
- `location`: 产检地点
- `prenatal_items`: 标准产检项目（多对多关系）
- `custom_prenatal_items`: 自定义产检项目（多对多关系）
- `travel_time`: 出行时间
- `reminder_time`: 提醒时间
- `preparation_notes`: 产检准备笔记
- `checkup_notes`: 产检记录笔记
- `checkup_images`: 产检单图片URL列表（JSON字段）
- `status`: 状态（scheduled/completed/cancelled/rescheduled）
- `created_at`: 创建时间
- `updated_at`: 更新时间

## API序列化器

模块提供了完整的REST API序列化器：

- `PrenatalItemSerializer`: 标准产检项目序列化
- `CustomPrenatalItemSerializer`: 自定义产检项目序列化
- `PrenatalCheckupSerializer`: 产检记录详细序列化
- `PrenatalCheckupListSerializer`: 产检记录列表序列化（简化版）

## Django Admin管理

所有模型都已注册到Django Admin，提供完整的后台管理功能：

- 标准产检项目管理
- 自定义产检项目管理
- 产检记录管理（支持筛选、搜索、批量操作）

## 数据库索引优化

为提高查询性能，已添加以下索引：

**PrenatalItem:**
- `(start_week, end_week)` - 孕周范围查询
- `name` - 项目名称查询

**PrenatalCheckup:**
- `(user, datetime)` - 用户产检时间查询
- `(user, status)` - 用户状态查询
- `datetime` - 时间排序
- `status` - 状态筛选

**CustomPrenatalItem:**
- `created_by` - 创建者查询

## 使用示例

### 创建产检记录

```python
from apps.prenatal.models import PrenatalCheckup, PrenatalItem
from apps.users.models import UserAccount
from django.utils import timezone
import datetime

# 获取用户和产检项目
user = UserAccount.objects.get(email='<EMAIL>')
blood_test = PrenatalItem.objects.get(name='血常规')

# 创建产检记录
checkup = PrenatalCheckup.objects.create(
    user=user,
    datetime=timezone.now() + datetime.timedelta(days=7),
    location='北京妇产医院',
    status='scheduled'
)

# 添加产检项目
checkup.prenatal_items.add(blood_test)
```

### 查询适用的产检项目

```python
# 查询20周适用的产检项目
week_20_items = PrenatalItem.objects.filter(
    start_week__lte=20,
    end_week__gte=20
)
```

## 测试

模块包含完整的单元测试，覆盖：

- 模型创建和验证
- 孕周范围验证
- 产检记录管理
- 图片添加/删除功能

运行测试：
```bash
uv run python manage.py test apps.prenatal
```

## 注意事项

1. **孕周验证**: 开始孕周不能大于结束孕周
2. **时间验证**: 产检时间不能是过去时间（创建时）
3. **提醒时间**: 提醒时间应该在出行时间之前
4. **图片存储**: 使用JSON字段存储图片URL列表，需要配合文件上传服务使用

## 扩展建议

1. **提醒功能**: 可集成定时任务发送产检提醒
2. **统计分析**: 添加产检数据统计和分析功能
3. **医院集成**: 可与医院系统对接，自动同步产检结果
4. **健康档案**: 与用户健康档案系统集成
