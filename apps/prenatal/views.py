from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from django.db import models
from .models import PrenatalItem, CustomPrenatalItem, PrenatalCheckup
from .serializers import (
    PrenatalItemSerializer,
    CustomPrenatalItemSerializer,
    PrenatalCheckupSerializer,
    PrenatalCheckupListSerializer
)


class PrenatalItemViewSet(viewsets.ReadOnlyModelViewSet):
    """标准产检项目视图集（只读）"""

    queryset = PrenatalItem.objects.all()
    serializer_class = PrenatalItemSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['start_week', 'end_week']
    search_fields = ['name', 'content']
    ordering_fields = ['start_week', 'end_week', 'name', 'created_at']
    ordering = ['start_week', 'name']

    @action(detail=False, methods=['get'])
    def by_week(self, request):
        """根据孕周获取适用的产检项目"""
        week = request.query_params.get('week')
        if not week:
            return Response({'error': '请提供孕周参数'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            week = int(week)
            if week < 1 or week > 42:
                return Response({'error': '孕周必须在1-42之间'}, status=status.HTTP_400_BAD_REQUEST)
        except ValueError:
            return Response({'error': '孕周必须是数字'}, status=status.HTTP_400_BAD_REQUEST)

        # 查询适用的产检项目
        items = self.queryset.filter(
            models.Q(start_week__isnull=True) | models.Q(start_week__lte=week),
            models.Q(end_week__isnull=True) | models.Q(end_week__gte=week)
        )

        serializer = self.get_serializer(items, many=True)
        return Response(serializer.data)


class CustomPrenatalItemViewSet(viewsets.ModelViewSet):
    """自定义产检项目视图集"""

    serializer_class = CustomPrenatalItemSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['start_week', 'end_week']
    search_fields = ['name', 'content']
    ordering_fields = ['start_week', 'end_week', 'name', 'created_at']
    ordering = ['start_week', 'name']

    def get_queryset(self):
        """只返回当前用户创建的自定义项目"""
        return CustomPrenatalItem.objects.filter(created_by=self.request.user)


class PrenatalCheckupViewSet(viewsets.ModelViewSet):
    """产检记录视图集"""

    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['datetime']
    search_fields = ['location', 'preparation_notes', 'checkup_notes']
    ordering_fields = ['datetime', 'created_at']
    ordering = ['-datetime']

    def get_queryset(self):
        """只返回当前用户的产检记录"""
        return PrenatalCheckup.objects.filter(user=self.request.user).prefetch_related(
            'prenatal_items', 'custom_prenatal_items'
        )

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return PrenatalCheckupListSerializer
        return PrenatalCheckupSerializer

    @action(detail=False, methods=['get'])
    def upcoming(self, request):
        """获取即将到来的产检"""
        upcoming_checkups = self.get_queryset().filter(
            datetime__gt=timezone.now()
        ).order_by('datetime')

        serializer = PrenatalCheckupListSerializer(upcoming_checkups, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def past_due(self, request):
        """获取已过期的产检"""
        past_due_checkups = self.get_queryset().filter(
            datetime__lt=timezone.now()
        ).order_by('-datetime')

        serializer = PrenatalCheckupListSerializer(past_due_checkups, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def add_image(self, request, pk=None):
        """添加产检图片（通过图片ID）"""
        checkup = self.get_object()
        image_id = request.data.get('image_id')
        image_url = request.data.get('image_url')

        if image_id:
            # 通过图片ID关联
            try:
                from .image_models import PrenatalImage
                image = PrenatalImage.objects.get(id=image_id, user=request.user)
                image.checkup = checkup
                image.save()

                # 同时添加到checkup_images列表
                if image.image_url and image.image_url not in checkup.checkup_images:
                    checkup.add_image(image.image_url)

                return Response({'message': '图片关联成功', 'image_url': image.image_url})
            except Exception as e:
                return Response({'error': f'图片关联失败: {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)

        elif image_url:
            # 通过URL添加（兼容旧方式）
            checkup.add_image(image_url)
            return Response({'message': '图片添加成功'})

        else:
            return Response({'error': '请提供图片ID或图片URL'}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def remove_image(self, request, pk=None):
        """移除产检图片"""
        checkup = self.get_object()
        image_id = request.data.get('image_id')
        image_url = request.data.get('image_url')

        if image_id:
            # 通过图片ID移除关联
            try:
                from .image_models import PrenatalImage
                image = PrenatalImage.objects.get(id=image_id, user=request.user, checkup=checkup)
                image.checkup = None
                image.save()

                # 同时从checkup_images列表移除
                if image.image_url and image.image_url in checkup.checkup_images:
                    checkup.remove_image(image.image_url)

                return Response({'message': '图片关联已移除'})
            except Exception as e:
                return Response({'error': f'移除图片关联失败: {str(e)}'}, status=status.HTTP_400_BAD_REQUEST)

        elif image_url:
            # 通过URL移除（兼容旧方式）
            checkup.remove_image(image_url)
            return Response({'message': '图片移除成功'})

        else:
            return Response({'error': '请提供图片ID或图片URL'}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'])
    def images(self, request, pk=None):
        """获取产检记录的所有图片"""
        checkup = self.get_object()

        # 获取关联的图片文件
        from .image_models import PrenatalImage
        from .image_serializers import PrenatalImageListSerializer

        images = PrenatalImage.objects.filter(checkup=checkup)
        serializer = PrenatalImageListSerializer(images, many=True, context={'request': request})

        return Response({
            'checkup_id': checkup.id,
            'image_files': serializer.data,
            'image_urls': checkup.checkup_images  # JSON字段中的URL列表
        })
