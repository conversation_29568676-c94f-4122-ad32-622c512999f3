from django.db import models
from django.core.validators import FileExtensionValidator
from django.utils import timezone
from apps.users.models import UserAccount
import uuid
import os


def prenatal_image_upload_path(instance, filename):
    """生成产检图片上传路径"""
    # 获取文件扩展名
    ext = filename.split('.')[-1].lower()
    # 生成新的文件名
    new_filename = f"{uuid.uuid4().hex}.{ext}"
    # 按年月分组存储
    date_path = timezone.now().strftime('%Y/%m')
    return f'prenatal/images/{date_path}/{new_filename}'


class PrenatalImage(models.Model):
    """产检图片模型"""
    
    IMAGE_TYPE_CHOICES = [
        ('checkup_report', '产检报告'),
        ('ultrasound', 'B超单'),
        ('blood_test', '血检报告'),
        ('urine_test', '尿检报告'),
        ('other', '其他'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        UserAccount,
        on_delete=models.CASCADE,
        verbose_name='用户',
        related_name='prenatal_images',
        help_text='上传图片的用户'
    )
    
    # 图片文件
    image = models.ImageField(
        '图片文件',
        upload_to=prenatal_image_upload_path,
        validators=[FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'])],
        help_text='支持的格式：jpg, jpeg, png, gif, bmp, webp'
    )
    
    # 图片信息
    image_type = models.CharField(
        '图片类型',
        max_length=20,
        choices=IMAGE_TYPE_CHOICES,
        default='other',
        help_text='图片类型分类'
    )
    title = models.CharField('图片标题', max_length=100, blank=True, help_text='图片的简短描述')
    description = models.TextField('图片描述', blank=True, help_text='详细描述图片内容')
    
    # 图片元数据
    file_size = models.PositiveIntegerField('文件大小', default=0, help_text='文件大小（字节）')
    width = models.PositiveIntegerField('图片宽度', default=0, help_text='图片宽度（像素）')
    height = models.PositiveIntegerField('图片高度', default=0, help_text='图片高度（像素）')
    
    # 关联产检记录（可选）
    checkup = models.ForeignKey(
        'PrenatalCheckup',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='关联产检记录',
        related_name='image_files',
        help_text='可选：关联到具体的产检记录'
    )
    
    # 时间戳
    created_at = models.DateTimeField('上传时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'prenatal_images'
        verbose_name = '产检图片'
        verbose_name_plural = '产检图片'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['user', 'image_type']),
            models.Index(fields=['checkup']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f'{self.user.email} - {self.get_image_type_display()} - {self.created_at.strftime("%Y-%m-%d")}'
    
    def save(self, *args, **kwargs):
        """保存时自动获取图片元数据"""
        if self.image and not self.file_size:
            try:
                # 获取文件大小
                self.file_size = self.image.size
                
                # 获取图片尺寸
                from PIL import Image
                with Image.open(self.image) as img:
                    self.width, self.height = img.size
            except Exception:
                # 如果获取失败，使用默认值
                pass
        
        super().save(*args, **kwargs)
    
    def delete(self, *args, **kwargs):
        """删除时同时删除文件"""
        if self.image:
            # 删除物理文件
            if os.path.isfile(self.image.path):
                os.remove(self.image.path)
        super().delete(*args, **kwargs)
    
    @property
    def file_size_human(self):
        """人类可读的文件大小"""
        if self.file_size < 1024:
            return f"{self.file_size} B"
        elif self.file_size < 1024 * 1024:
            return f"{self.file_size / 1024:.1f} KB"
        else:
            return f"{self.file_size / (1024 * 1024):.1f} MB"
    
    @property
    def image_url(self):
        """获取图片URL"""
        if self.image:
            return self.image.url
        return None
    
    @property
    def thumbnail_url(self):
        """获取缩略图URL（如果需要的话）"""
        # 这里可以集成缩略图生成服务
        return self.image_url
    
    def get_absolute_url(self):
        """获取图片的绝对URL"""
        return self.image_url


class ImageUploadSession(models.Model):
    """图片上传会话模型 - 用于批量上传管理"""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        UserAccount,
        on_delete=models.CASCADE,
        verbose_name='用户',
        related_name='upload_sessions'
    )
    
    # 会话信息
    session_name = models.CharField('会话名称', max_length=100, blank=True)
    total_files = models.PositiveIntegerField('总文件数', default=0)
    uploaded_files = models.PositiveIntegerField('已上传文件数', default=0)
    failed_files = models.PositiveIntegerField('失败文件数', default=0)
    
    # 状态
    STATUS_CHOICES = [
        ('pending', '等待中'),
        ('uploading', '上传中'),
        ('completed', '已完成'),
        ('failed', '失败'),
    ]
    status = models.CharField(
        '状态',
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending'
    )
    
    # 关联产检记录（可选）
    checkup = models.ForeignKey(
        'PrenatalCheckup',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='关联产检记录',
        related_name='upload_sessions'
    )
    
    # 时间戳
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    completed_at = models.DateTimeField('完成时间', null=True, blank=True)
    
    class Meta:
        db_table = 'prenatal_image_upload_sessions'
        verbose_name = '图片上传会话'
        verbose_name_plural = '图片上传会话'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f'{self.user.email} - {self.session_name or "上传会话"} - {self.get_status_display()}'
    
    @property
    def progress_percentage(self):
        """上传进度百分比"""
        if self.total_files == 0:
            return 0
        return (self.uploaded_files / self.total_files) * 100
    
    @property
    def is_completed(self):
        """是否已完成"""
        return self.status == 'completed'
    
    def mark_completed(self):
        """标记为完成"""
        self.status = 'completed'
        self.completed_at = timezone.now()
        self.save(update_fields=['status', 'completed_at'])
    
    def increment_uploaded(self):
        """增加已上传文件数"""
        self.uploaded_files += 1
        if self.uploaded_files >= self.total_files:
            self.mark_completed()
        else:
            self.save(update_fields=['uploaded_files'])
    
    def increment_failed(self):
        """增加失败文件数"""
        self.failed_files += 1
        self.save(update_fields=['failed_files'])
