from django.contrib import admin
from django.utils.html import format_html
from .models import PrenatalItem, CustomPrenatalItem, PrenatalCheckup


@admin.register(PrenatalItem)
class PrenatalItemAdmin(admin.ModelAdmin):
    """标准产检项目管理"""

    list_display = ['name', 'week_range', 'created_at']
    list_filter = ['start_week', 'end_week', 'created_at']
    search_fields = ['name', 'content']
    ordering = ['start_week', 'name']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'content')
        }),
        ('适用孕周', {
            'fields': ('start_week', 'end_week'),
            'description': '设置该项目适用的孕周范围（1-42周）'
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def week_range(self, obj):
        """显示孕周范围"""
        if obj.start_week and obj.end_week:
            return f'{obj.start_week}-{obj.end_week}周'
        elif obj.start_week:
            return f'{obj.start_week}周+'
        elif obj.end_week:
            return f'≤{obj.end_week}周'
        return '不限'
    week_range.short_description = '适用孕周'


@admin.register(CustomPrenatalItem)
class CustomPrenatalItemAdmin(admin.ModelAdmin):
    """自定义产检项目管理"""

    list_display = ['name', 'created_by', 'week_range', 'created_at']
    list_filter = ['created_by', 'start_week', 'end_week', 'created_at']
    search_fields = ['name', 'content', 'created_by__email']
    ordering = ['created_by', 'start_week', 'name']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'content', 'created_by')
        }),
        ('适用孕周', {
            'fields': ('start_week', 'end_week'),
            'description': '设置该项目适用的孕周范围（1-42周）'
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def week_range(self, obj):
        """显示孕周范围"""
        if obj.start_week and obj.end_week:
            return f'{obj.start_week}-{obj.end_week}周'
        elif obj.start_week:
            return f'{obj.start_week}周+'
        elif obj.end_week:
            return f'≤{obj.end_week}周'
        return '不限'
    week_range.short_description = '适用孕周'


@admin.register(PrenatalCheckup)
class PrenatalCheckupAdmin(admin.ModelAdmin):
    """产检记录管理"""

    list_display = ['user_email', 'datetime', 'location', 'status', 'items_count', 'created_at']
    list_filter = ['status', 'datetime', 'created_at']
    search_fields = ['user__email', 'location', 'preparation_notes', 'checkup_notes']
    ordering = ['-datetime']
    readonly_fields = ['created_at', 'updated_at']
    date_hierarchy = 'datetime'

    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'datetime', 'location', 'status')
        }),
        ('产检项目', {
            'fields': ('prenatal_items', 'custom_prenatal_items'),
            'description': '选择需要进行的产检项目'
        }),
        ('时间安排', {
            'fields': ('travel_time', 'reminder_time'),
            'classes': ('collapse',)
        }),
        ('笔记记录', {
            'fields': ('preparation_notes', 'checkup_notes'),
            'classes': ('collapse',)
        }),
        ('图片记录', {
            'fields': ('checkup_images',),
            'classes': ('collapse',),
            'description': '产检单、B超单等图片URL列表'
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    filter_horizontal = ['prenatal_items', 'custom_prenatal_items']

    def user_email(self, obj):
        """显示用户邮箱"""
        return obj.user.email
    user_email.short_description = '用户邮箱'

    def items_count(self, obj):
        """显示产检项目数量"""
        standard_count = obj.prenatal_items.count()
        custom_count = obj.custom_prenatal_items.count()
        total = standard_count + custom_count
        return format_html(
            '<span title="标准项目: {}, 自定义项目: {}">{}</span>',
            standard_count, custom_count, total
        )
    items_count.short_description = '项目数量'

    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('user').prefetch_related(
            'prenatal_items', 'custom_prenatal_items'
        )
