# 产检图片服务文档

## 概述

产检图片服务为Align Backend论坛系统提供完整的图片上传、存储和管理功能，专门用于产检相关图片的处理，包括产检报告、B超单、血检报告等医疗图片。

## 功能特性

### 🖼️ 图片管理
- **多格式支持**: jpg, jpeg, png, gif, bmp, webp
- **文件大小限制**: 单个文件最大10MB
- **自动元数据提取**: 文件大小、图片尺寸
- **分类管理**: 产检报告、B超单、血检报告、尿检报告、其他
- **关联产检记录**: 可选择关联到具体的产检记录

### 📤 上传功能
- **单张上传**: 快速上传单张图片
- **批量上传**: 一次最多上传20张图片
- **上传会话**: 批量上传进度跟踪
- **错误处理**: 完善的上传失败处理机制

### 🗂️ 存储组织
- **按时间分组**: 图片按年/月自动分组存储
- **UUID文件名**: 避免文件名冲突
- **路径结构**: `prenatal/images/YYYY/MM/uuid.ext`

## 数据模型

### PrenatalImage (产检图片)

**字段说明:**
- `id`: UUID主键
- `user`: 用户外键（关联UserAccount）
- `image`: 图片文件字段
- `image_type`: 图片类型（产检报告/B超单/血检报告/尿检报告/其他）
- `title`: 图片标题
- `description`: 图片描述
- `file_size`: 文件大小（字节）
- `width/height`: 图片尺寸
- `checkup`: 关联产检记录（可选）
- `created_at/updated_at`: 时间戳

**属性方法:**
- `file_size_human`: 人类可读的文件大小
- `image_url`: 图片访问URL
- `thumbnail_url`: 缩略图URL

### ImageUploadSession (上传会话)

**字段说明:**
- `id`: UUID主键
- `user`: 用户外键
- `session_name`: 会话名称
- `total_files`: 总文件数
- `uploaded_files`: 已上传文件数
- `failed_files`: 失败文件数
- `status`: 状态（pending/uploading/completed/failed）
- `checkup`: 关联产检记录（可选）

## API端点

### 图片管理 (`/api/prenatal/images/`)

#### 基础CRUD操作
```http
GET    /api/prenatal/images/           # 获取图片列表
POST   /api/prenatal/images/           # 上传单张图片
GET    /api/prenatal/images/{id}/      # 获取图片详情
PUT    /api/prenatal/images/{id}/      # 更新图片信息
DELETE /api/prenatal/images/{id}/      # 删除图片
```

#### 特殊操作
```http
POST   /api/prenatal/images/batch_upload/     # 批量上传
GET    /api/prenatal/images/by_type/?type=    # 按类型筛选
GET    /api/prenatal/images/by_checkup/?checkup_id=  # 按产检记录筛选
GET    /api/prenatal/images/recent/?days=7    # 获取最近上传
GET    /api/prenatal/images/statistics/       # 获取统计信息
```

#### 快速上传
```http
POST   /api/prenatal/images/quick-upload/     # 快速上传单张图片
GET    /api/prenatal/images/types/            # 获取图片类型选项
```

### 上传会话 (`/api/prenatal/upload-sessions/`)

```http
GET    /api/prenatal/upload-sessions/         # 获取上传会话列表
GET    /api/prenatal/upload-sessions/{id}/    # 获取会话详情
GET    /api/prenatal/upload-sessions/{id}/images/  # 获取会话中的图片
GET    /api/prenatal/upload-sessions/active/  # 获取活跃会话
```

### 产检记录图片管理

```http
GET    /api/prenatal/checkups/{id}/images/    # 获取产检记录的所有图片
POST   /api/prenatal/checkups/{id}/add_image/ # 关联图片到产检记录
POST   /api/prenatal/checkups/{id}/remove_image/ # 移除图片关联
```

## 使用示例

### 1. 单张图片上传

```javascript
// 使用FormData上传
const formData = new FormData();
formData.append('image', file);
formData.append('image_type', 'ultrasound');
formData.append('title', 'B超检查');
formData.append('description', '20周大排畸检查');

fetch('/api/prenatal/images/', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token
    },
    body: formData
})
.then(response => response.json())
.then(data => {
    console.log('上传成功:', data);
});
```

### 2. 批量图片上传

```javascript
const formData = new FormData();
files.forEach(file => {
    formData.append('images', file);
});
formData.append('image_type', 'checkup_report');
formData.append('session_name', '产检报告批量上传');

fetch('/api/prenatal/images/batch_upload/', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token
    },
    body: formData
})
.then(response => response.json())
.then(data => {
    console.log('批量上传结果:', data);
});
```

### 3. 快速上传

```javascript
const formData = new FormData();
formData.append('image', file);
formData.append('image_type', 'other');

fetch('/api/prenatal/images/quick-upload/', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token
    },
    body: formData
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('图片URL:', data.image_url);
    }
});
```

### 4. 关联图片到产检记录

```javascript
// 通过图片ID关联
fetch('/api/prenatal/checkups/123/add_image/', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        image_id: 'image-uuid-here'
    })
});

// 通过图片URL关联（兼容旧方式）
fetch('/api/prenatal/checkups/123/add_image/', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        image_url: 'https://example.com/image.jpg'
    })
});
```

### 5. 获取图片统计

```javascript
fetch('/api/prenatal/images/statistics/', {
    headers: {
        'Authorization': 'Bearer ' + token
    }
})
.then(response => response.json())
.then(data => {
    console.log('总图片数:', data.total_count);
    console.log('总大小:', data.total_size_human);
    console.log('最近7天上传:', data.recent_count);
    console.log('分类统计:', data.type_statistics);
});
```

## 权限控制

- **用户认证**: 所有API都需要用户登录
- **数据隔离**: 用户只能访问自己上传的图片
- **关联验证**: 只能关联自己的产检记录

## 文件存储

### 存储路径
```
media/
└── prenatal/
    └── images/
        ├── 2024/
        │   ├── 01/
        │   │   ├── uuid1.jpg
        │   │   └── uuid2.png
        │   └── 02/
        └── 2025/
```

### 文件命名
- 使用UUID避免文件名冲突
- 保留原始文件扩展名
- 格式：`{uuid}.{extension}`

## 安全考虑

1. **文件类型验证**: 只允许图片格式
2. **文件大小限制**: 单文件最大10MB
3. **文件扫描**: 建议集成病毒扫描
4. **访问控制**: 用户只能访问自己的图片
5. **HTTPS传输**: 生产环境使用HTTPS

## 性能优化

1. **数据库索引**: 已添加必要的查询索引
2. **查询优化**: 使用select_related和prefetch_related
3. **缩略图**: 可集成缩略图生成服务
4. **CDN**: 生产环境建议使用CDN加速

## 扩展建议

1. **图片压缩**: 自动压缩大尺寸图片
2. **缩略图生成**: 自动生成多种尺寸缩略图
3. **OCR识别**: 对医疗报告进行文字识别
4. **云存储**: 集成阿里云OSS、腾讯云COS等
5. **图片标签**: AI自动识别图片内容并打标签
