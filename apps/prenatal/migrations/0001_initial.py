# Generated by Django 5.2.4 on 2025-08-10 18:06

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PrenatalItem',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, verbose_name='项目名称')),
                ('content', models.TextField(blank=True, help_text='详细描述产检项目的内容和注意事项', verbose_name='项目详细内容')),
                ('start_week', models.PositiveIntegerField(blank=True, help_text='适用的开始孕周（1-42周）', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(42)], verbose_name='适用开始孕周')),
                ('end_week', models.PositiveIntegerField(blank=True, help_text='适用的结束孕周（1-42周）', null=True, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(42)], verbose_name='适用结束孕周')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '标准产检项目',
                'verbose_name_plural': '标准产检项目',
                'db_table': 'prenatal_items',
                'ordering': ['start_week', 'name'],
            },
        ),
        migrations.CreateModel(
            name='PrenatalCheckup',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('datetime', models.DateTimeField(help_text='预约的产检日期和时间', verbose_name='产检日期和时间')),
                ('location', models.CharField(help_text='医院或诊所名称及地址', max_length=200, verbose_name='产检地点')),
                ('travel_time', models.TimeField(blank=True, help_text='计划出发时间', null=True, verbose_name='出行时间')),
                ('reminder_time', models.TimeField(blank=True, help_text='提醒时间', null=True, verbose_name='提醒时间')),
                ('preparation_notes', models.TextField(blank=True, help_text='产检前的准备事项、注意事项等', verbose_name='产检准备笔记')),
                ('checkup_notes', models.TextField(blank=True, help_text='产检过程中的记录、医生建议等', verbose_name='产检笔记')),
                ('checkup_images', models.JSONField(blank=True, default=list, help_text='产检单、B超单等图片的URL列表', verbose_name='产检单图片')),
                ('status', models.CharField(choices=[('scheduled', '已预约'), ('completed', '已完成'), ('cancelled', '已取消'), ('rescheduled', '已改期')], default='scheduled', help_text='产检状态', max_length=20, verbose_name='状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '产检记录',
                'verbose_name_plural': '产检记录',
                'db_table': 'prenatal_checkups',
                'ordering': ['-datetime'],
            },
        ),
        migrations.CreateModel(
            name='CustomPrenatalItem',
            fields=[
                ('prenatalitem_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='prenatal.prenatalitem')),
            ],
            options={
                'verbose_name': '自定义产检项目',
                'verbose_name_plural': '自定义产检项目',
                'db_table': 'custom_prenatal_items',
                'ordering': ['created_by', 'prenatalitem_ptr__start_week', 'prenatalitem_ptr__name'],
            },
            bases=('prenatal.prenatalitem',),
        ),
        migrations.AddIndex(
            model_name='prenatalitem',
            index=models.Index(fields=['start_week', 'end_week'], name='prenatal_it_start_w_43c885_idx'),
        ),
        migrations.AddIndex(
            model_name='prenatalitem',
            index=models.Index(fields=['name'], name='prenatal_it_name_b348b5_idx'),
        ),
        migrations.AddField(
            model_name='prenatalcheckup',
            name='prenatal_items',
            field=models.ManyToManyField(blank=True, help_text='选择的标准产检项目', related_name='standard_checkups', to='prenatal.prenatalitem', verbose_name='标准产检项目'),
        ),
        migrations.AddField(
            model_name='prenatalcheckup',
            name='user',
            field=models.ForeignKey(help_text='进行产检的用户', on_delete=django.db.models.deletion.CASCADE, related_name='prenatal_checkups', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AddField(
            model_name='prenatalcheckup',
            name='custom_prenatal_items',
            field=models.ManyToManyField(blank=True, help_text='选择的自定义产检项目', related_name='custom_checkups', to='prenatal.customprenatalitem', verbose_name='自定义产检项目'),
        ),
        migrations.AddField(
            model_name='customprenatalitem',
            name='created_by',
            field=models.ForeignKey(help_text='创建该自定义项目的用户', on_delete=django.db.models.deletion.CASCADE, related_name='custom_prenatal_items', to=settings.AUTH_USER_MODEL, verbose_name='创建者'),
        ),
        migrations.AddIndex(
            model_name='prenatalcheckup',
            index=models.Index(fields=['user', 'datetime'], name='prenatal_ch_user_id_4e3dec_idx'),
        ),
        migrations.AddIndex(
            model_name='prenatalcheckup',
            index=models.Index(fields=['user', 'status'], name='prenatal_ch_user_id_bf90dc_idx'),
        ),
        migrations.AddIndex(
            model_name='prenatalcheckup',
            index=models.Index(fields=['datetime'], name='prenatal_ch_datetim_2f8819_idx'),
        ),
        migrations.AddIndex(
            model_name='prenatalcheckup',
            index=models.Index(fields=['status'], name='prenatal_ch_status_c9263c_idx'),
        ),
        migrations.AddIndex(
            model_name='customprenatalitem',
            index=models.Index(fields=['created_by'], name='custom_pren_created_88a99b_idx'),
        ),
    ]
