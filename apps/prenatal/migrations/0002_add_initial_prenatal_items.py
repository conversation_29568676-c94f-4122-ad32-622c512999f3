# Generated by Django 5.2.4 on 2025-08-10 18:06

from django.db import migrations
import uuid


def add_initial_prenatal_items(apps, schema_editor):
    """添加初始的标准产检项目"""
    PrenatalItem = apps.get_model('prenatal', 'PrenatalItem')

    initial_items = [
        # 早期产检项目 (6-12周)
        {
            'name': '建档检查',
            'content': '包括基本体格检查、血常规、尿常规、肝肾功能、血型、传染病筛查等',
            'start_week': 6,
            'end_week': 12,
        },
        {
            'name': 'NT检查',
            'content': '胎儿颈项透明层厚度测量，用于早期筛查染色体异常',
            'start_week': 11,
            'end_week': 13,
        },
        {
            'name': '早期唐氏筛查',
            'content': '通过血清学检查筛查胎儿染色体异常风险',
            'start_week': 9,
            'end_week': 13,
        },

        # 中期产检项目 (14-28周)
        {
            'name': '中期唐氏筛查',
            'content': '血清学筛查，检测胎儿染色体异常和神经管缺陷风险',
            'start_week': 15,
            'end_week': 20,
        },
        {
            'name': '四维彩超',
            'content': '大排畸检查，全面筛查胎儿结构异常',
            'start_week': 20,
            'end_week': 24,
        },
        {
            'name': '糖耐量试验',
            'content': '妊娠期糖尿病筛查，需要空腹12小时',
            'start_week': 24,
            'end_week': 28,
        },

        # 晚期产检项目 (28-40周)
        {
            'name': '胎心监护',
            'content': '监测胎儿心率变化，评估胎儿宫内状况',
            'start_week': 32,
            'end_week': 42,
        },
        {
            'name': '生物物理评分',
            'content': '通过超声评估胎儿呼吸运动、胎动、肌张力、羊水量等',
            'start_week': 36,
            'end_week': 42,
        },
        {
            'name': '骨盆测量',
            'content': '评估骨盆大小，判断是否适合自然分娩',
            'start_week': 36,
            'end_week': 38,
        },

        # 常规检查项目
        {
            'name': '血常规',
            'content': '检查血红蛋白、白细胞、血小板等，监测贫血和感染',
            'start_week': 1,
            'end_week': 42,
        },
        {
            'name': '尿常规',
            'content': '检查尿蛋白、尿糖等，监测肾功能和妊娠期并发症',
            'start_week': 1,
            'end_week': 42,
        },
        {
            'name': '体重血压测量',
            'content': '监测孕期体重增长和血压变化',
            'start_week': 1,
            'end_week': 42,
        },
        {
            'name': '宫高腹围测量',
            'content': '评估胎儿生长发育情况',
            'start_week': 20,
            'end_week': 42,
        },

        # 特殊检查项目
        {
            'name': '羊水穿刺',
            'content': '诊断性检查，用于确诊胎儿染色体异常',
            'start_week': 16,
            'end_week': 20,
        },
        {
            'name': '无创DNA检测',
            'content': '通过母体血液检测胎儿染色体异常，准确率较高',
            'start_week': 12,
            'end_week': 22,
        },
    ]

    for item_data in initial_items:
        PrenatalItem.objects.create(
            id=uuid.uuid4(),
            **item_data
        )


def remove_initial_prenatal_items(apps, schema_editor):
    """删除初始的标准产检项目"""
    PrenatalItem = apps.get_model('prenatal', 'PrenatalItem')

    item_names = [
        '建档检查', 'NT检查', '早期唐氏筛查', '中期唐氏筛查', '四维彩超',
        '糖耐量试验', '胎心监护', '生物物理评分', '骨盆测量', '血常规',
        '尿常规', '体重血压测量', '宫高腹围测量', '羊水穿刺', '无创DNA检测'
    ]

    PrenatalItem.objects.filter(name__in=item_names).delete()


class Migration(migrations.Migration):

    dependencies = [
        ('prenatal', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(
            add_initial_prenatal_items,
            remove_initial_prenatal_items,
        ),
    ]
