# Generated by Django 5.2.4 on 2025-08-10 18:19

import apps.prenatal.image_models
import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('prenatal', '0003_remove_status_field'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ImageUploadSession',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('session_name', models.CharField(blank=True, max_length=100, verbose_name='会话名称')),
                ('total_files', models.PositiveIntegerField(default=0, verbose_name='总文件数')),
                ('uploaded_files', models.PositiveIntegerField(default=0, verbose_name='已上传文件数')),
                ('failed_files', models.PositiveIntegerField(default=0, verbose_name='失败文件数')),
                ('status', models.CharField(choices=[('pending', '等待中'), ('uploading', '上传中'), ('completed', '已完成'), ('failed', '失败')], default='pending', max_length=20, verbose_name='状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='完成时间')),
                ('checkup', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='upload_sessions', to='prenatal.prenatalcheckup', verbose_name='关联产检记录')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='upload_sessions', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '图片上传会话',
                'verbose_name_plural': '图片上传会话',
                'db_table': 'prenatal_image_upload_sessions',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'status'], name='prenatal_im_user_id_bb33d5_idx'), models.Index(fields=['created_at'], name='prenatal_im_created_0c033b_idx')],
            },
        ),
        migrations.CreateModel(
            name='PrenatalImage',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('image', models.ImageField(help_text='支持的格式：jpg, jpeg, png, gif, bmp, webp', upload_to=apps.prenatal.image_models.prenatal_image_upload_path, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'])], verbose_name='图片文件')),
                ('image_type', models.CharField(choices=[('checkup_report', '产检报告'), ('ultrasound', 'B超单'), ('blood_test', '血检报告'), ('urine_test', '尿检报告'), ('other', '其他')], default='other', help_text='图片类型分类', max_length=20, verbose_name='图片类型')),
                ('title', models.CharField(blank=True, help_text='图片的简短描述', max_length=100, verbose_name='图片标题')),
                ('description', models.TextField(blank=True, help_text='详细描述图片内容', verbose_name='图片描述')),
                ('file_size', models.PositiveIntegerField(default=0, help_text='文件大小（字节）', verbose_name='文件大小')),
                ('width', models.PositiveIntegerField(default=0, help_text='图片宽度（像素）', verbose_name='图片宽度')),
                ('height', models.PositiveIntegerField(default=0, help_text='图片高度（像素）', verbose_name='图片高度')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='上传时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('checkup', models.ForeignKey(blank=True, help_text='可选：关联到具体的产检记录', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='image_files', to='prenatal.prenatalcheckup', verbose_name='关联产检记录')),
                ('user', models.ForeignKey(help_text='上传图片的用户', on_delete=django.db.models.deletion.CASCADE, related_name='prenatal_images', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '产检图片',
                'verbose_name_plural': '产检图片',
                'db_table': 'prenatal_images',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'created_at'], name='prenatal_im_user_id_45ff41_idx'), models.Index(fields=['user', 'image_type'], name='prenatal_im_user_id_71dc1d_idx'), models.Index(fields=['checkup'], name='prenatal_im_checkup_3b107d_idx'), models.Index(fields=['created_at'], name='prenatal_im_created_30d319_idx')],
            },
        ),
    ]
