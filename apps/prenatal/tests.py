from django.test import TestCase
from django.utils import timezone
from django.core.exceptions import ValidationError
from apps.users.models import UserAccount
from .models import PrenatalItem, CustomPrenatalItem, PrenatalCheckup
import datetime


class PrenatalItemModelTest(TestCase):
    """标准产检项目模型测试"""

    def test_create_prenatal_item(self):
        """测试创建标准产检项目"""
        item = PrenatalItem.objects.create(
            name='血常规',
            content='检查血红蛋白、白细胞等',
            start_week=1,
            end_week=42
        )
        self.assertEqual(item.name, '血常规')
        self.assertEqual(str(item), '血常规 (1-42周)')

    def test_week_range_validation(self):
        """测试孕周范围验证"""
        item = PrenatalItem(
            name='测试项目',
            start_week=20,
            end_week=10  # 错误：开始周大于结束周
        )
        with self.assertRaises(ValidationError):
            item.clean()

    def test_is_applicable_for_week(self):
        """测试孕周适用性检查"""
        item = PrenatalItem.objects.create(
            name='NT检查',
            start_week=11,
            end_week=13
        )
        self.assertTrue(item.is_applicable_for_week(12))
        self.assertFalse(item.is_applicable_for_week(10))
        self.assertFalse(item.is_applicable_for_week(15))


class CustomPrenatalItemModelTest(TestCase):
    """自定义产检项目模型测试"""

    def setUp(self):
        self.user = UserAccount.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

    def test_create_custom_prenatal_item(self):
        """测试创建自定义产检项目"""
        item = CustomPrenatalItem.objects.create(
            name='自定义检查',
            content='用户自定义的检查项目',
            created_by=self.user,
            start_week=20,
            end_week=24
        )
        self.assertEqual(item.created_by, self.user)
        self.assertIn('by <EMAIL>', str(item))


class PrenatalCheckupModelTest(TestCase):
    """产检记录模型测试"""

    def setUp(self):
        self.user = UserAccount.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.prenatal_item = PrenatalItem.objects.create(
            name='血常规',
            content='检查血红蛋白等'
        )
        self.custom_item = CustomPrenatalItem.objects.create(
            name='自定义检查',
            created_by=self.user
        )

    def test_create_prenatal_checkup(self):
        """测试创建产检记录"""
        future_time = timezone.now() + datetime.timedelta(days=7)
        checkup = PrenatalCheckup.objects.create(
            user=self.user,
            datetime=future_time,
            location='测试医院'
        )
        checkup.prenatal_items.add(self.prenatal_item)
        checkup.custom_prenatal_items.add(self.custom_item)

        self.assertEqual(checkup.user, self.user)
        self.assertEqual(checkup.location, '测试医院')
        self.assertEqual(len(checkup.get_all_items()), 2)

    def test_is_upcoming_property(self):
        """测试即将到来的产检判断"""
        future_time = timezone.now() + datetime.timedelta(days=7)
        checkup = PrenatalCheckup.objects.create(
            user=self.user,
            datetime=future_time,
            location='测试医院'
        )
        self.assertTrue(checkup.is_upcoming)

    def test_add_remove_images(self):
        """测试添加和移除图片"""
        checkup = PrenatalCheckup.objects.create(
            user=self.user,
            datetime=timezone.now() + datetime.timedelta(days=7),
            location='测试医院'
        )

        # 添加图片
        checkup.add_image('http://example.com/image1.jpg')
        checkup.add_image('http://example.com/image2.jpg')
        self.assertEqual(len(checkup.checkup_images), 2)

        # 移除图片
        checkup.remove_image('http://example.com/image1.jpg')
        self.assertEqual(len(checkup.checkup_images), 1)
        self.assertNotIn('http://example.com/image1.jpg', checkup.checkup_images)
