from rest_framework import serializers
from .models import PrenatalItem, CustomPrenatalItem, PrenatalCheckup


class PrenatalItemSerializer(serializers.ModelSerializer):
    """标准产检项目序列化器"""
    
    week_range = serializers.SerializerMethodField()
    
    class Meta:
        model = PrenatalItem
        fields = [
            'id', 'name', 'content', 'start_week', 'end_week', 
            'week_range', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'week_range']
    
    def get_week_range(self, obj):
        """获取孕周范围描述"""
        if obj.start_week and obj.end_week:
            return f'{obj.start_week}-{obj.end_week}周'
        elif obj.start_week:
            return f'{obj.start_week}周+'
        elif obj.end_week:
            return f'≤{obj.end_week}周'
        return '不限'


class CustomPrenatalItemSerializer(serializers.ModelSerializer):
    """自定义产检项目序列化器"""
    
    created_by_email = serializers.CharField(source='created_by.email', read_only=True)
    week_range = serializers.SerializerMethodField()
    
    class Meta:
        model = CustomPrenatalItem
        fields = [
            'id', 'name', 'content', 'start_week', 'end_week', 
            'created_by', 'created_by_email', 'week_range', 
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_by_email', 'week_range', 'created_at', 'updated_at']
    
    def get_week_range(self, obj):
        """获取孕周范围描述"""
        if obj.start_week and obj.end_week:
            return f'{obj.start_week}-{obj.end_week}周'
        elif obj.start_week:
            return f'{obj.start_week}周+'
        elif obj.end_week:
            return f'≤{obj.end_week}周'
        return '不限'
    
    def create(self, validated_data):
        """创建自定义产检项目时自动设置创建者"""
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class PrenatalCheckupSerializer(serializers.ModelSerializer):
    """产检记录序列化器"""
    
    user_email = serializers.CharField(source='user.email', read_only=True)
    prenatal_items_detail = PrenatalItemSerializer(source='prenatal_items', many=True, read_only=True)
    custom_prenatal_items_detail = CustomPrenatalItemSerializer(source='custom_prenatal_items', many=True, read_only=True)
    items_count = serializers.SerializerMethodField()
    is_upcoming = serializers.ReadOnlyField()
    is_past_due = serializers.ReadOnlyField()
    
    class Meta:
        model = PrenatalCheckup
        fields = [
            'id', 'user', 'user_email', 'datetime', 'location', 
            'prenatal_items', 'custom_prenatal_items',
            'prenatal_items_detail', 'custom_prenatal_items_detail',
            'travel_time', 'reminder_time', 'preparation_notes', 
            'checkup_notes', 'checkup_images', 'status',
            'items_count', 'is_upcoming', 'is_past_due',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'user_email', 'prenatal_items_detail', 
            'custom_prenatal_items_detail', 'items_count', 
            'is_upcoming', 'is_past_due', 'created_at', 'updated_at'
        ]
    
    def get_items_count(self, obj):
        """获取产检项目总数"""
        standard_count = obj.prenatal_items.count()
        custom_count = obj.custom_prenatal_items.count()
        return {
            'standard': standard_count,
            'custom': custom_count,
            'total': standard_count + custom_count
        }
    
    def create(self, validated_data):
        """创建产检记录时自动设置用户"""
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)
    
    def validate_datetime(self, value):
        """验证产检时间"""
        from django.utils import timezone
        
        # 只在创建时验证时间不能是过去
        if not self.instance and value < timezone.now():
            raise serializers.ValidationError('产检时间不能是过去时间')
        return value
    
    def validate(self, data):
        """验证整体数据"""
        # 验证提醒时间和出行时间的关系
        reminder_time = data.get('reminder_time')
        travel_time = data.get('travel_time')
        
        if reminder_time and travel_time and reminder_time > travel_time:
            raise serializers.ValidationError({
                'reminder_time': '提醒时间应该在出行时间之前'
            })
        
        return data


class PrenatalCheckupListSerializer(serializers.ModelSerializer):
    """产检记录列表序列化器（简化版）"""
    
    items_count = serializers.SerializerMethodField()
    is_upcoming = serializers.ReadOnlyField()
    
    class Meta:
        model = PrenatalCheckup
        fields = [
            'id', 'datetime', 'location', 'status', 
            'items_count', 'is_upcoming', 'created_at'
        ]
    
    def get_items_count(self, obj):
        """获取产检项目总数"""
        return obj.prenatal_items.count() + obj.custom_prenatal_items.count()
