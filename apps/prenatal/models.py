from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from apps.users.models import UserAccount
import uuid


class PrenatalItem(models.Model):
    """标准产检项目表"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField('项目名称', max_length=100)
    content = models.TextField('项目详细内容', blank=True, help_text='详细描述产检项目的内容和注意事项')
    start_week = models.PositiveIntegerField(
        '适用开始孕周',
        blank=True,
        null=True,
        validators=[MinValueValidator(1), MaxValueValidator(42)],
        help_text='适用的开始孕周（1-42周）'
    )
    end_week = models.PositiveIntegerField(
        '适用结束孕周',
        blank=True,
        null=True,
        validators=[MinValueValidator(1), MaxValueValidator(42)],
        help_text='适用的结束孕周（1-42周）'
    )

    # 时间戳
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'prenatal_items'
        verbose_name = '标准产检项目'
        verbose_name_plural = '标准产检项目'
        ordering = ['start_week', 'name']
        indexes = [
            models.Index(fields=['start_week', 'end_week']),
            models.Index(fields=['name']),
        ]

    def __str__(self):
        if self.start_week and self.end_week:
            return f'{self.name} ({self.start_week}-{self.end_week}周)'
        elif self.start_week:
            return f'{self.name} ({self.start_week}周+)'
        elif self.end_week:
            return f'{self.name} (≤{self.end_week}周)'
        return self.name

    def clean(self):
        """验证孕周范围"""
        from django.core.exceptions import ValidationError
        if self.start_week and self.end_week and self.start_week > self.end_week:
            raise ValidationError('开始孕周不能大于结束孕周')

    def is_applicable_for_week(self, week):
        """检查是否适用于指定孕周"""
        if not week:
            return True
        if self.start_week and week < self.start_week:
            return False
        if self.end_week and week > self.end_week:
            return False
        return True


class CustomPrenatalItem(PrenatalItem):
    """自定义产检项目表 - 继承标准产检项目"""

    created_by = models.ForeignKey(
        UserAccount,
        on_delete=models.CASCADE,
        verbose_name='创建者',
        related_name='custom_prenatal_items',
        help_text='创建该自定义项目的用户'
    )

    class Meta:
        db_table = 'custom_prenatal_items'
        verbose_name = '自定义产检项目'
        verbose_name_plural = '自定义产检项目'
        ordering = ['created_by', 'prenatalitem_ptr__start_week', 'prenatalitem_ptr__name']
        indexes = [
            models.Index(fields=['created_by']),
        ]

    def __str__(self):
        base_str = super().__str__()
        return f'{base_str} (by {self.created_by.email})'


class PrenatalCheckup(models.Model):
    """产检记录表"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        UserAccount,
        on_delete=models.CASCADE,
        verbose_name='用户',
        related_name='prenatal_checkups',
        help_text='进行产检的用户'
    )
    datetime = models.DateTimeField('产检日期和时间', help_text='预约的产检日期和时间')
    location = models.CharField('产检地点', max_length=200, help_text='医院或诊所名称及地址')

    # 多对多关系
    prenatal_items = models.ManyToManyField(
        PrenatalItem,
        verbose_name='标准产检项目',
        blank=True,
        related_name='standard_checkups',
        help_text='选择的标准产检项目'
    )
    custom_prenatal_items = models.ManyToManyField(
        CustomPrenatalItem,
        verbose_name='自定义产检项目',
        blank=True,
        related_name='custom_checkups',
        help_text='选择的自定义产检项目'
    )

    # 时间相关字段
    travel_time = models.TimeField('出行时间', blank=True, null=True, help_text='计划出发时间')
    reminder_time = models.TimeField('提醒时间', blank=True, null=True, help_text='提醒时间')

    # 笔记字段
    preparation_notes = models.TextField(
        '产检准备笔记',
        blank=True,
        help_text='产检前的准备事项、注意事项等'
    )
    checkup_notes = models.TextField(
        '产检笔记',
        blank=True,
        help_text='产检过程中的记录、医生建议等'
    )

    # 图片字段 - 使用JSONField存储图片URL列表
    checkup_images = models.JSONField(
        '产检单图片',
        default=list,
        blank=True,
        help_text='产检单、B超单等图片的URL列表'
    )


    # 时间戳
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'prenatal_checkups'
        verbose_name = '产检记录'
        verbose_name_plural = '产检记录'
        ordering = ['-datetime']
        indexes = [
            models.Index(fields=['user', 'datetime']),
            models.Index(fields=['datetime']),
        ]

    def __str__(self):
        return f'{self.user.email} - {self.datetime.strftime("%Y-%m-%d %H:%M")} - {self.location}'

    def clean(self):
        """验证数据"""
        from django.core.exceptions import ValidationError

        # 验证产检时间不能是过去时间（创建时）
        if not self.pk and self.datetime and self.datetime < timezone.now():
            raise ValidationError('产检时间不能是过去时间')

        # 验证提醒时间应该在产检时间之前
        if self.reminder_time and self.travel_time and self.reminder_time > self.travel_time:
            raise ValidationError('提醒时间应该在出行时间之前')

    @property
    def is_upcoming(self):
        """是否是即将到来的产检"""
        return self.datetime > timezone.now()

    @property
    def is_past_due(self):
        """是否已过期"""
        return self.datetime < timezone.now()

    def get_all_items(self):
        """获取所有产检项目（标准+自定义）"""
        standard_items = list(self.prenatal_items.all())
        custom_items = list(self.custom_prenatal_items.all())
        return standard_items + custom_items

    def add_image(self, image_url):
        """添加产检图片"""
        if not self.checkup_images:
            self.checkup_images = []
        self.checkup_images.append(image_url)
        self.save(update_fields=['checkup_images'])

    def remove_image(self, image_url):
        """移除产检图片"""
        if self.checkup_images and image_url in self.checkup_images:
            self.checkup_images.remove(image_url)
            self.save(update_fields=['checkup_images'])
