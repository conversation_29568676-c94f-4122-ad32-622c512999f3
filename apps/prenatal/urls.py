from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from .views import PrenatalItemViewSet, CustomPrenatalItemViewSet, PrenatalCheckupViewSet

# 创建路由器
router = DefaultRouter()
router.register(r'items', PrenatalItemViewSet, basename='prenatal-items')
router.register(r'custom-items', CustomPrenatalItemViewSet, basename='custom-prenatal-items')
router.register(r'checkups', PrenatalCheckupViewSet, basename='prenatal-checkups')

app_name = 'prenatal'

urlpatterns = [
    path('', include(router.urls)),
]
