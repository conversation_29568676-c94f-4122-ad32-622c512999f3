from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import PrenatalItemViewSet, CustomPrenatalItemViewSet, PrenatalCheckupViewSet
from .image_views import PrenatalImageViewSet, ImageUploadSessionViewSet, quick_upload, image_types

# 创建路由器
router = DefaultRouter()
router.register(r'items', PrenatalItemViewSet, basename='prenatal-items')
router.register(r'custom-items', CustomPrenatalItemViewSet, basename='custom-prenatal-items')
router.register(r'checkups', PrenatalCheckupViewSet, basename='prenatal-checkups')
router.register(r'images', PrenatalImageViewSet, basename='prenatal-images')
router.register(r'upload-sessions', ImageUploadSessionViewSet, basename='upload-sessions')

app_name = 'prenatal'

urlpatterns = [
    # 图片相关的额外路由
    path('images/quick-upload/', quick_upload, name='quick-upload'),
    path('images/types/', image_types, name='image-types'),

    # 包含路由器生成的URL
    path('', include(router.urls)),
]
