from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from rest_framework_simplejwt.tokens import RefreshToken
from drf_spectacular.utils import extend_schema
from django.contrib.auth import logout

from .serializers import (
    CustomTokenObtainPairSerializer,
    EmailCodeLoginSerializer,
    SendVerificationCodeSerializer,
    LogoutSerializer
)


class CustomTokenObtainPairView(TokenObtainPairView):
    """自定义JWT Token获取视图"""

    serializer_class = CustomTokenObtainPairSerializer

    @extend_schema(
        summary="邮箱密码登录",
        description="使用邮箱和密码进行登录，返回JWT Token",
        request=CustomTokenObtainPairSerializer,
        responses={
            200: {
                "type": "object",
                "properties": {
                    "refresh": {"type": "string", "description": "刷新Token"},
                    "access": {"type": "string", "description": "访问Token"},
                    "user": {
                        "type": "object",
                        "properties": {
                            "id": {"type": "integer"},
                            "email": {"type": "string"},
                            "username": {"type": "string"},
                            "nickname": {"type": "string"},
                            "avatar_url": {"type": "string"}
                        }
                    }
                }
            },
            400: {"description": "登录失败"}
        },
        tags=['auth']
    )
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)


class EmailCodeLoginView(APIView):
    """邮箱验证码登录视图"""

    permission_classes = [permissions.AllowAny]

    @extend_schema(
        summary="邮箱验证码登录",
        description="使用邮箱验证码进行登录",
        request=EmailCodeLoginSerializer,
        responses={
            200: {
                "type": "object",
                "properties": {
                    "refresh": {"type": "string", "description": "刷新Token"},
                    "access": {"type": "string", "description": "访问Token"},
                    "user": {
                        "type": "object",
                        "properties": {
                            "id": {"type": "integer"},
                            "email": {"type": "string"},
                            "username": {"type": "string"},
                            "nickname": {"type": "string"},
                            "avatar_url": {"type": "string"}
                        }
                    }
                }
            },
            400: {"description": "验证码错误或已过期"}
        },
        tags=['auth']
    )
    def post(self, request):
        serializer = EmailCodeLoginSerializer(
            data=request.data,
            context={'request': request}
        )
        if serializer.is_valid():
            return Response(serializer.validated_data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class SendVerificationCodeView(APIView):
    """发送验证码视图"""

    permission_classes = [permissions.AllowAny]

    @extend_schema(
        summary="发送邮箱验证码",
        description="向指定邮箱发送验证码，支持注册、登录、重置密码等场景",
        request=SendVerificationCodeSerializer,
        responses={
            200: {"description": "验证码发送成功"},
            400: {"description": "请求参数错误"}
        },
        tags=['auth']
    )
    def post(self, request):
        serializer = SendVerificationCodeSerializer(
            data=request.data,
            context={'request': request}
        )
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"detail": "验证码已发送，请查收邮件"},
                status=status.HTTP_200_OK
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class LogoutView(APIView):
    """登出视图"""

    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="用户登出",
        description="登出当前用户，将刷新Token加入黑名单",
        request=LogoutSerializer,
        responses={
            200: {"description": "登出成功"},
            400: {"description": "Token无效"}
        },
        tags=['auth']
    )
    def post(self, request):
        serializer = LogoutSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(
                {"detail": "登出成功"},
                status=status.HTTP_200_OK
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@extend_schema(
    summary="验证Token有效性",
    description="验证当前用户的Token是否有效",
    responses={
        200: {
            "type": "object",
            "properties": {
                "valid": {"type": "boolean", "description": "Token是否有效"},
                "user": {
                    "type": "object",
                    "properties": {
                        "id": {"type": "integer"},
                        "email": {"type": "string"},
                        "username": {"type": "string"},
                        "nickname": {"type": "string"}
                    }
                }
            }
        },
        401: {"description": "Token无效或已过期"}
    },
    tags=['auth']
)
@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def verify_token(request):
    """验证Token有效性"""
    user = request.user
    return Response({
        'valid': True,
        'user': {
            'id': user.id,
            'email': user.email,
            'username': user.username,
            'nickname': user.nickname,
            'avatar_url': user.get_avatar_url()
        }
    })
