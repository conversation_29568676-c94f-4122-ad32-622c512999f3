from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import authenticate
from django.utils import timezone
from apps.users.models import User, EmailVerificationCode, LoginLog


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    """自定义JWT Token获取序列化器"""
    
    email = serializers.EmailField(required=True)
    password = serializers.CharField(required=True, write_only=True)
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 移除默认的username字段，使用email
        self.fields.pop('username', None)
    
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)
        # 添加自定义声明
        token['email'] = user.email
        token['username'] = user.username
        token['nickname'] = user.nickname
        return token
    
    def validate(self, attrs):
        email = attrs.get('email')
        password = attrs.get('password')
        
        if email and password:
            user = authenticate(
                request=self.context.get('request'),
                username=email,  # 使用email作为username
                password=password
            )
            
            if not user:
                raise serializers.ValidationError('邮箱或密码错误')
            
            if not user.is_active:
                raise serializers.ValidationError('用户账户已被禁用')
            
            # 记录登录日志
            self._log_login_attempt(user, email, 'password', 'success')
            
            # 更新最后登录IP
            request = self.context.get('request')
            if request:
                user.last_login_ip = self._get_client_ip(request)
                user.save(update_fields=['last_login_ip'])
            
            refresh = self.get_token(user)
            
            return {
                'refresh': str(refresh),
                'access': str(refresh.access_token),
                'user': {
                    'id': user.id,
                    'email': user.email,
                    'username': user.username,
                    'nickname': user.nickname,
                    'avatar_url': user.get_avatar_url(),
                }
            }
        else:
            raise serializers.ValidationError('必须提供邮箱和密码')
    
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def _log_login_attempt(self, user, email, login_type, status, failure_reason=''):
        """记录登录尝试"""
        request = self.context.get('request')
        LoginLog.objects.create(
            user=user if status == 'success' else None,
            email=email,
            login_type=login_type,
            status=status,
            ip_address=self._get_client_ip(request) if request else '',
            user_agent=request.META.get('HTTP_USER_AGENT', '') if request else '',
            failure_reason=failure_reason
        )


class EmailCodeLoginSerializer(serializers.Serializer):
    """邮箱验证码登录序列化器"""
    
    email = serializers.EmailField(required=True)
    code = serializers.CharField(required=True, max_length=6)
    
    def validate(self, attrs):
        email = attrs.get('email')
        code = attrs.get('code')
        
        # 验证验证码
        try:
            verification_code = EmailVerificationCode.objects.get(
                email=email,
                code=code,
                code_type='login',
                is_used=False
            )
            
            if verification_code.is_expired():
                raise serializers.ValidationError('验证码已过期')
            
            # 标记验证码为已使用
            verification_code.mark_as_used()
            
        except EmailVerificationCode.DoesNotExist:
            raise serializers.ValidationError('验证码错误或已失效')
        
        # 获取或创建用户
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            raise serializers.ValidationError('用户不存在，请先注册')
        
        if not user.is_active:
            raise serializers.ValidationError('用户账户已被禁用')
        
        # 记录登录日志
        self._log_login_attempt(user, email, 'email_code', 'success')
        
        # 更新最后登录信息
        request = self.context.get('request')
        if request:
            user.last_login_ip = self._get_client_ip(request)
            user.last_login = timezone.now()
            user.save(update_fields=['last_login_ip', 'last_login'])
        
        # 生成JWT Token
        refresh = RefreshToken.for_user(user)
        
        return {
            'refresh': str(refresh),
            'access': str(refresh.access_token),
            'user': {
                'id': user.id,
                'email': user.email,
                'username': user.username,
                'nickname': user.nickname,
                'avatar_url': user.get_avatar_url(),
            }
        }
    
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def _log_login_attempt(self, user, email, login_type, status, failure_reason=''):
        """记录登录尝试"""
        request = self.context.get('request')
        LoginLog.objects.create(
            user=user if status == 'success' else None,
            email=email,
            login_type=login_type,
            status=status,
            ip_address=self._get_client_ip(request) if request else '',
            user_agent=request.META.get('HTTP_USER_AGENT', '') if request else '',
            failure_reason=failure_reason
        )


class SendVerificationCodeSerializer(serializers.Serializer):
    """发送验证码序列化器"""
    
    email = serializers.EmailField(required=True)
    code_type = serializers.ChoiceField(
        choices=EmailVerificationCode.CODE_TYPE_CHOICES,
        required=True
    )
    
    def validate_email(self, value):
        """验证邮箱"""
        code_type = self.initial_data.get('code_type')
        
        if code_type in ['login', 'reset_password']:
            # 登录和重置密码需要用户已存在
            if not User.objects.filter(email=value).exists():
                raise serializers.ValidationError('该邮箱尚未注册')
        elif code_type == 'register':
            # 注册需要用户不存在
            if User.objects.filter(email=value).exists():
                raise serializers.ValidationError('该邮箱已被注册')
        
        return value
    
    def create(self, validated_data):
        """创建并发送验证码"""
        from apps.users.serializers import EmailVerificationCodeSerializer
        
        request = self.context.get('request')
        context = {
            'ip_address': self._get_client_ip(request) if request else '',
            'user_agent': request.META.get('HTTP_USER_AGENT', '') if request else ''
        }
        
        serializer = EmailVerificationCodeSerializer(
            data=validated_data,
            context=context
        )
        serializer.is_valid(raise_exception=True)
        return serializer.save()
    
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class LogoutSerializer(serializers.Serializer):
    """登出序列化器"""
    
    refresh = serializers.CharField(required=True)
    
    def validate(self, attrs):
        self.token = attrs['refresh']
        return attrs
    
    def save(self, **kwargs):
        try:
            RefreshToken(self.token).blacklist()
        except Exception:
            raise serializers.ValidationError('Token无效')
