"""
测试预产期字段功能
"""
from django.test import TestCase
from django.contrib.auth import get_user_model
from datetime import date, timedelta
from apps.users.models import UserProfile
from apps.users.serializers import UserProfileSerializer, UserUpdateSerializer


User = get_user_model()


class ExpectedDeliveryDateTestCase(TestCase):
    """预产期字段测试用例"""

    def setUp(self):
        """设置测试数据"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.profile = self.user.profile

    def test_expected_delivery_date_field_exists(self):
        """测试预产期字段是否存在"""
        self.assertTrue(hasattr(self.profile, 'expected_delivery_date'))

    def test_expected_delivery_date_default_value(self):
        """测试预产期字段默认值"""
        self.assertIsNone(self.profile.expected_delivery_date)

    def test_set_expected_delivery_date(self):
        """测试设置预产期"""
        future_date = date.today() + timedelta(days=270)  # 约9个月后
        self.profile.expected_delivery_date = future_date
        self.profile.save()

        # 重新从数据库读取
        self.profile.refresh_from_db()
        self.assertEqual(self.profile.expected_delivery_date, future_date)

    def test_expected_delivery_date_in_serializer(self):
        """测试预产期字段在序列化器中"""
        future_date = date.today() + timedelta(days=270)
        self.profile.expected_delivery_date = future_date
        self.profile.save()

        serializer = UserProfileSerializer(self.profile)
        self.assertIn('expected_delivery_date', serializer.data)
        self.assertEqual(serializer.data['expected_delivery_date'], future_date.isoformat())

    def test_update_expected_delivery_date_via_serializer(self):
        """测试通过序列化器更新预产期"""
        future_date = date.today() + timedelta(days=280)
        data = {
            'expected_delivery_date': future_date.isoformat()
        }

        serializer = UserUpdateSerializer(self.profile, data=data, partial=True)
        self.assertTrue(serializer.is_valid())
        
        updated_profile = serializer.save()
        self.assertEqual(updated_profile.expected_delivery_date, future_date)

    def test_expected_delivery_date_can_be_null(self):
        """测试预产期可以为空"""
        # 先设置一个日期
        future_date = date.today() + timedelta(days=270)
        self.profile.expected_delivery_date = future_date
        self.profile.save()

        # 然后设置为None
        self.profile.expected_delivery_date = None
        self.profile.save()

        self.profile.refresh_from_db()
        self.assertIsNone(self.profile.expected_delivery_date)

    def test_expected_delivery_date_validation(self):
        """测试预产期字段验证"""
        # 测试有效日期
        valid_date = date.today() + timedelta(days=270)
        data = {'expected_delivery_date': valid_date.isoformat()}
        
        serializer = UserUpdateSerializer(self.profile, data=data, partial=True)
        self.assertTrue(serializer.is_valid())

        # 测试无效日期格式
        invalid_data = {'expected_delivery_date': 'invalid-date'}
        serializer = UserUpdateSerializer(self.profile, data=invalid_data, partial=True)
        self.assertFalse(serializer.is_valid())
        self.assertIn('expected_delivery_date', serializer.errors)
