# Generated by Django 5.2.4 on 2025-07-11 10:06

import django.contrib.auth.models
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('first_name', models.Char<PERSON>ield(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('email', models.EmailField(max_length=254, unique=True, verbose_name='邮箱地址')),
                ('username', models.CharField(max_length=150, unique=True, verbose_name='用户名')),
                ('nickname', models.CharField(blank=True, max_length=50, verbose_name='昵称')),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='avatars/', verbose_name='头像')),
                ('bio', models.TextField(blank=True, max_length=500, verbose_name='个人简介')),
                ('is_email_verified', models.BooleanField(default=False, verbose_name='邮箱已验证')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='手机号')),
                ('is_phone_verified', models.BooleanField(default=False, verbose_name='手机已验证')),
                ('receive_email_notifications', models.BooleanField(default=True, verbose_name='接收邮件通知')),
                ('receive_sms_notifications', models.BooleanField(default=False, verbose_name='接收短信通知')),
                ('post_count', models.PositiveIntegerField(default=0, verbose_name='发帖数')),
                ('comment_count', models.PositiveIntegerField(default=0, verbose_name='评论数')),
                ('like_count', models.PositiveIntegerField(default=0, verbose_name='获赞数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='注册时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('last_login_ip', models.GenericIPAddressField(blank=True, null=True, verbose_name='最后登录IP')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': '用户',
                'verbose_name_plural': '用户',
                'db_table': 'users',
                'ordering': ['-created_at'],
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='EmailVerificationCode',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('email', models.EmailField(max_length=254, verbose_name='邮箱地址')),
                ('code', models.CharField(max_length=6, verbose_name='验证码')),
                ('code_type', models.CharField(choices=[('register', '注册验证'), ('login', '登录验证'), ('reset_password', '重置密码'), ('change_email', '更换邮箱')], max_length=20, verbose_name='验证码类型')),
                ('is_used', models.BooleanField(default=False, verbose_name='已使用')),
                ('used_at', models.DateTimeField(blank=True, null=True, verbose_name='使用时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('expires_at', models.DateTimeField(verbose_name='过期时间')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='请求IP')),
                ('user_agent', models.TextField(blank=True, verbose_name='用户代理')),
            ],
            options={
                'verbose_name': '邮箱验证码',
                'verbose_name_plural': '邮箱验证码',
                'db_table': 'email_verification_codes',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['email', 'code_type', 'is_used'], name='email_verif_email_edf78a_idx'), models.Index(fields=['created_at'], name='email_verif_created_028ca8_idx')],
            },
        ),
        migrations.CreateModel(
            name='LoginLog',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('email', models.EmailField(max_length=254, verbose_name='登录邮箱')),
                ('login_type', models.CharField(choices=[('password', '密码登录'), ('email_code', '邮箱验证码登录'), ('phone_code', '手机验证码登录'), ('social', '第三方登录')], max_length=20, verbose_name='登录方式')),
                ('status', models.CharField(choices=[('success', '成功'), ('failed', '失败'), ('blocked', '被阻止')], max_length=20, verbose_name='登录状态')),
                ('ip_address', models.GenericIPAddressField(verbose_name='IP地址')),
                ('user_agent', models.TextField(verbose_name='用户代理')),
                ('location', models.CharField(blank=True, max_length=100, verbose_name='登录地点')),
                ('failure_reason', models.CharField(blank=True, max_length=200, verbose_name='失败原因')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='登录时间')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '登录日志',
                'verbose_name_plural': '登录日志',
                'db_table': 'login_logs',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['user', 'created_at'], name='login_logs_user_id_783da5_idx'), models.Index(fields=['email', 'status'], name='login_logs_email_4ab659_idx'), models.Index(fields=['ip_address'], name='login_logs_ip_addr_669c36_idx')],
            },
        ),
    ]
