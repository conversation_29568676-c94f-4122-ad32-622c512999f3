# Generated by Django 5.2.4 on 2025-08-07 16:48

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('email', models.EmailField(max_length=254, unique=True, verbose_name='邮箱地址')),
                ('is_active', models.BooleanField(default=True, verbose_name='激活状态')),
                ('is_staff', models.BooleanField(default=False, verbose_name='管理员状态')),
                ('is_superuser', models.BooleanField(default=False, verbose_name='超级用户状态')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='注册时间')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='最后登录时间')),
                ('last_login_ip', models.GenericIPAddressField(blank=True, null=True, verbose_name='最后登录IP')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to.', related_name='useraccount_set', related_query_name='useraccount', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='useraccount_set', related_query_name='useraccount', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': '用户账户',
                'verbose_name_plural': '用户账户',
                'db_table': 'user_accounts',
                'ordering': ['-date_joined'],
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('username', models.CharField(max_length=150, unique=True, verbose_name='用户名')),
                ('nickname', models.CharField(blank=True, max_length=50, verbose_name='昵称')),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='avatars/', verbose_name='头像')),
                ('bio', models.TextField(blank=True, max_length=500, verbose_name='个人简介')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='手机号')),
                ('is_email_verified', models.BooleanField(default=False, verbose_name='邮箱已验证')),
                ('is_phone_verified', models.BooleanField(default=False, verbose_name='手机已验证')),
                ('receive_email_notifications', models.BooleanField(default=True, verbose_name='接收邮件通知')),
                ('receive_sms_notifications', models.BooleanField(default=False, verbose_name='接收短信通知')),
                ('post_count', models.PositiveIntegerField(default=0, verbose_name='发帖数')),
                ('comment_count', models.PositiveIntegerField(default=0, verbose_name='评论数')),
                ('like_count', models.PositiveIntegerField(default=0, verbose_name='获赞数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to='users.useraccount', verbose_name='用户账户')),
            ],
            options={
                'verbose_name': '用户资料',
                'verbose_name_plural': '用户资料',
                'db_table': 'user_profiles',
                'ordering': ['-created_at'],
            },
        ),
    ]
