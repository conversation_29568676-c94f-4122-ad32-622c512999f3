# Generated by Django 5.2.4 on 2025-08-07 16:48

from django.db import migrations


def migrate_user_data_forward(apps, schema_editor):
    """将现有User数据迁移到UserAccount和UserProfile"""
    User = apps.get_model('users', 'User')
    UserAccount = apps.get_model('users', 'UserAccount')
    UserProfile = apps.get_model('users', 'UserProfile')

    # 获取数据库连接
    db_alias = schema_editor.connection.alias

    # 迁移每个用户的数据
    for old_user in User.objects.using(db_alias).all():
        # 创建UserAccount
        user_account = UserAccount.objects.using(db_alias).create(
            id=old_user.id,  # 保持相同的ID
            email=old_user.email,
            password=old_user.password,
            is_active=old_user.is_active,
            is_staff=old_user.is_staff,
            is_superuser=old_user.is_superuser,
            date_joined=old_user.date_joined,
            last_login=old_user.last_login,
            last_login_ip=old_user.last_login_ip,
        )

        # 复制用户组和权限
        user_account.groups.set(old_user.groups.all())
        user_account.user_permissions.set(old_user.user_permissions.all())

        # 创建UserProfile
        UserProfile.objects.using(db_alias).create(
            user=user_account,
            username=old_user.username,
            nickname=old_user.nickname,
            avatar=old_user.avatar,
            bio=old_user.bio,
            phone=old_user.phone,
            is_email_verified=old_user.is_email_verified,
            is_phone_verified=old_user.is_phone_verified,
            receive_email_notifications=old_user.receive_email_notifications,
            receive_sms_notifications=old_user.receive_sms_notifications,
            post_count=old_user.post_count,
            comment_count=old_user.comment_count,
            like_count=old_user.like_count,
            created_at=old_user.created_at,
            updated_at=old_user.updated_at,
        )


def migrate_user_data_reverse(apps, schema_editor):
    """回滚数据迁移"""
    User = apps.get_model('users', 'User')
    UserAccount = apps.get_model('users', 'UserAccount')
    UserProfile = apps.get_model('users', 'UserProfile')

    # 获取数据库连接
    db_alias = schema_editor.connection.alias

    # 从UserAccount和UserProfile恢复到User
    for user_account in UserAccount.objects.using(db_alias).all():
        try:
            profile = user_account.profile
        except UserProfile.DoesNotExist:
            continue

        # 创建或更新User记录
        User.objects.using(db_alias).update_or_create(
            id=user_account.id,
            defaults={
                'email': user_account.email,
                'password': user_account.password,
                'is_active': user_account.is_active,
                'is_staff': user_account.is_staff,
                'is_superuser': user_account.is_superuser,
                'date_joined': user_account.date_joined,
                'last_login': user_account.last_login,
                'last_login_ip': user_account.last_login_ip,
                'username': profile.username,
                'nickname': profile.nickname,
                'avatar': profile.avatar,
                'bio': profile.bio,
                'phone': profile.phone,
                'is_email_verified': profile.is_email_verified,
                'is_phone_verified': profile.is_phone_verified,
                'receive_email_notifications': profile.receive_email_notifications,
                'receive_sms_notifications': profile.receive_sms_notifications,
                'post_count': profile.post_count,
                'comment_count': profile.comment_count,
                'like_count': profile.like_count,
                'created_at': profile.created_at,
                'updated_at': profile.updated_at,
            }
        )


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0002_add_new_user_models'),
    ]

    operations = [
        migrations.RunPython(
            migrate_user_data_forward,
            migrate_user_data_reverse,
        ),
    ]
