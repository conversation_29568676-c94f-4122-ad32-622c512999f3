# Generated by Django 5.2.4 on 2025-08-07 17:11

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0004_update_foreign_keys'),
    ]

    operations = [
        migrations.AddField(
            model_name='userprofile',
            name='estimated_due_date',
            field=models.DateTimeField(blank=True, help_text='用户相关任务或活动的预计到期日期', null=True, verbose_name='预计到期日期'),
        ),
        migrations.AlterField(
            model_name='loginlog',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='users.user', verbose_name='用户'),
        ),
    ]
