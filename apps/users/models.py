from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin, BaseUserManager, AbstractUser
from django.db import models
from django.utils import timezone
import uuid


class UserAccountManager(BaseUserManager):
    """自定义用户管理器"""

    def create_user(self, email, password=None, **extra_fields):
        """创建普通用户"""
        if not email:
            raise ValueError('用户必须有邮箱地址')

        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)

        # 用户资料将通过信号自动创建

        return user

    def create_superuser(self, email, password=None, **extra_fields):
        """创建超级用户"""
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError('超级用户必须设置is_staff=True')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('超级用户必须设置is_superuser=True')

        return self.create_user(email, password, **extra_fields)


class UserAccount(AbstractBaseUser, PermissionsMixin):
    """用户认证账户模型 - 包含敏感认证信息"""

    # 基础认证信息
    email = models.EmailField('邮箱地址', unique=True)

    # 权限状态
    is_active = models.BooleanField('激活状态', default=True)
    is_staff = models.BooleanField('管理员状态', default=False)
    is_superuser = models.BooleanField('超级用户状态', default=False)

    # 时间戳
    date_joined = models.DateTimeField('注册时间', default=timezone.now)
    last_login = models.DateTimeField('最后登录时间', blank=True, null=True)
    last_login_ip = models.GenericIPAddressField('最后登录IP', blank=True, null=True)

    # 权限关系 - 使用不同的related_name避免冲突
    groups = models.ManyToManyField(
        'auth.Group',
        verbose_name='groups',
        blank=True,
        help_text='The groups this user belongs to.',
        related_name='useraccount_set',
        related_query_name='useraccount',
    )
    user_permissions = models.ManyToManyField(
        'auth.Permission',
        verbose_name='user permissions',
        blank=True,
        help_text='Specific permissions for this user.',
        related_name='useraccount_set',
        related_query_name='useraccount',
    )

    # 使用自定义管理器
    objects = UserAccountManager()

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []

    class Meta:
        db_table = 'user_accounts'
        verbose_name = '用户账户'
        verbose_name_plural = '用户账户'
        ordering = ['-date_joined']

    def __str__(self):
        return self.email

    @property
    def username(self):
        """兼容性属性 - 返回用户资料中的用户名"""
        try:
            return self.profile.username
        except:
            return self.email.split('@')[0]

    @property
    def display_name(self):
        """显示名称"""
        try:
            return self.profile.display_name
        except:
            return self.username


class UserProfile(models.Model):
    """用户资料模型 - 包含个人信息和设置"""

    # 关联用户账户
    user = models.OneToOneField(
        UserAccount,
        on_delete=models.CASCADE,
        verbose_name='用户账户',
        related_name='profile'
    )

    # 基本信息
    username = models.CharField('用户名', max_length=150, unique=True)
    nickname = models.CharField('昵称', max_length=50, blank=True)
    avatar = models.ImageField('头像', upload_to='avatars/', blank=True, null=True)
    bio = models.TextField('个人简介', max_length=500, blank=True)

    # 联系信息
    phone = models.CharField('手机号', max_length=20, blank=True)
    expected_delivery_date = models.DateField('预产期', blank=True, null=True, help_text='预计分娩日期')

    # 验证状态
    is_email_verified = models.BooleanField('邮箱已验证', default=False)
    is_phone_verified = models.BooleanField('手机已验证', default=False)

    # 用户设置
    receive_email_notifications = models.BooleanField('接收邮件通知', default=True)
    receive_sms_notifications = models.BooleanField('接收短信通知', default=False)

    # 统计信息
    post_count = models.PositiveIntegerField('发帖数', default=0)
    comment_count = models.PositiveIntegerField('评论数', default=0)
    like_count = models.PositiveIntegerField('获赞数', default=0)

    # 时间戳
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        db_table = 'user_profiles'
        verbose_name = '用户资料'
        verbose_name_plural = '用户资料'
        ordering = ['-created_at']

    def __str__(self):
        return f'{self.username} ({self.user.email})'

    @property
    def display_name(self):
        """显示名称：优先使用昵称，其次用户名"""
        return self.nickname or self.username

    def get_avatar_url(self):
        """获取头像URL"""
        if self.avatar:
            return self.avatar.url
        return '/static/images/default-avatar.png'


# 保留原User模型用于数据迁移
class User(AbstractUser):
    """原始用户模型 - 保留用于数据迁移"""

    # 使用邮箱作为用户名
    email = models.EmailField('邮箱地址', unique=True)
    username = models.CharField('用户名', max_length=150, unique=True)

    # 用户基本信息
    nickname = models.CharField('昵称', max_length=50, blank=True)
    avatar = models.ImageField('头像', upload_to='avatars/', blank=True, null=True)
    bio = models.TextField('个人简介', max_length=500, blank=True)

    # 用户状态
    is_email_verified = models.BooleanField('邮箱已验证', default=False)
    phone = models.CharField('手机号', max_length=20, blank=True)
    is_phone_verified = models.BooleanField('手机已验证', default=False)

    # 用户设置
    receive_email_notifications = models.BooleanField('接收邮件通知', default=True)
    receive_sms_notifications = models.BooleanField('接收短信通知', default=False)

    # 统计信息
    post_count = models.PositiveIntegerField('发帖数', default=0)
    comment_count = models.PositiveIntegerField('评论数', default=0)
    like_count = models.PositiveIntegerField('获赞数', default=0)

    # 时间戳
    created_at = models.DateTimeField('注册时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    last_login_ip = models.GenericIPAddressField('最后登录IP', blank=True, null=True)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']

    class Meta:
        db_table = 'users'
        verbose_name = '用户'
        verbose_name_plural = '用户'
        ordering = ['-created_at']

    def __str__(self):
        return self.email

    @property
    def display_name(self):
        """显示名称：优先使用昵称，其次用户名"""
        return self.nickname or self.username

    def get_avatar_url(self):
        """获取头像URL"""
        if self.avatar:
            return self.avatar.url
        return '/static/images/default-avatar.png'


class EmailVerificationCode(models.Model):
    """邮箱验证码模型"""

    CODE_TYPE_CHOICES = [
        ('register', '注册验证'),
        ('login', '登录验证'),
        ('reset_password', '重置密码'),
        ('change_email', '更换邮箱'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField('邮箱地址')
    code = models.CharField('验证码', max_length=6)
    code_type = models.CharField('验证码类型', max_length=20, choices=CODE_TYPE_CHOICES)

    # 验证状态
    is_used = models.BooleanField('已使用', default=False)
    used_at = models.DateTimeField('使用时间', blank=True, null=True)

    # 时间戳
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    expires_at = models.DateTimeField('过期时间')

    # 安全相关
    ip_address = models.GenericIPAddressField('请求IP', blank=True, null=True)
    user_agent = models.TextField('用户代理', blank=True)

    class Meta:
        db_table = 'email_verification_codes'
        verbose_name = '邮箱验证码'
        verbose_name_plural = '邮箱验证码'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['email', 'code_type', 'is_used']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f'{self.email} - {self.get_code_type_display()}'

    def is_expired(self):
        """检查验证码是否过期"""
        return timezone.now() > self.expires_at

    def is_valid(self):
        """检查验证码是否有效"""
        return not self.is_used and not self.is_expired()

    def mark_as_used(self):
        """标记验证码为已使用"""
        self.is_used = True
        self.used_at = timezone.now()
        self.save(update_fields=['is_used', 'used_at'])


class LoginLog(models.Model):
    """登录日志模型"""

    LOGIN_TYPE_CHOICES = [
        ('password', '密码登录'),
        ('email_code', '邮箱验证码登录'),
        ('phone_code', '手机验证码登录'),
        ('social', '第三方登录'),
    ]

    STATUS_CHOICES = [
        ('success', '成功'),
        ('failed', '失败'),
        ('blocked', '被阻止'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name='用户', null=True, blank=True)
    email = models.EmailField('登录邮箱')
    login_type = models.CharField('登录方式', max_length=20, choices=LOGIN_TYPE_CHOICES)
    status = models.CharField('登录状态', max_length=20, choices=STATUS_CHOICES)

    # 请求信息
    ip_address = models.GenericIPAddressField('IP地址')
    user_agent = models.TextField('用户代理')
    location = models.CharField('登录地点', max_length=100, blank=True)

    # 失败原因
    failure_reason = models.CharField('失败原因', max_length=200, blank=True)

    # 时间戳
    created_at = models.DateTimeField('登录时间', auto_now_add=True)

    class Meta:
        db_table = 'login_logs'
        verbose_name = '登录日志'
        verbose_name_plural = '登录日志'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['email', 'status']),
            models.Index(fields=['ip_address']),
        ]

    def __str__(self):
        return f'{self.email} - {self.get_status_display()} - {self.created_at}'


# 信号处理器 - 自动创建用户资料
from django.db.models.signals import post_save
from django.dispatch import receiver

@receiver(post_save, sender=UserAccount)
def create_user_profile(sender, instance, created, **kwargs):
    """当创建UserAccount时自动创建UserProfile"""
    if created:
        # 生成默认用户名
        username = instance.email.split('@')[0]
        # 确保用户名唯一
        counter = 1
        original_username = username
        while UserProfile.objects.filter(username=username).exists():
            username = f"{original_username}{counter}"
            counter += 1

        UserProfile.objects.create(user=instance, username=username)

@receiver(post_save, sender=UserAccount)
def save_user_profile(sender, instance, **kwargs):
    """保存UserAccount时同时保存UserProfile"""
    if hasattr(instance, 'profile'):
        instance.profile.save()
