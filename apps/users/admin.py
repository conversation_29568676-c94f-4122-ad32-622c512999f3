from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from .models import UserAccount, UserProfile, EmailVerificationCode, LoginLog


class UserProfileInline(admin.StackedInline):
    """用户资料内联编辑"""
    model = UserProfile
    can_delete = False
    verbose_name_plural = '用户资料'
    fields = [
        'username', 'nickname', 'avatar', 'bio', 'phone', 'expected_delivery_date',
        'is_email_verified', 'is_phone_verified',
        'receive_email_notifications', 'receive_sms_notifications',
        'post_count', 'comment_count', 'like_count'
    ]
    readonly_fields = ['post_count', 'comment_count', 'like_count']


@admin.register(UserAccount)
class UserAccountAdmin(BaseUserAdmin):
    """用户账户管理"""

    inlines = [UserProfileInline]

    list_display = [
        'email', 'get_username', 'get_nickname', 'is_active',
        'is_staff', 'is_superuser', 'date_joined', 'last_login'
    ]
    list_filter = [
        'is_active', 'is_staff', 'is_superuser', 'date_joined', 'last_login'
    ]
    search_fields = ['email', 'profile__username', 'profile__nickname']
    readonly_fields = ['date_joined', 'last_login', 'last_login_ip']

    fieldsets = (
        ('认证信息', {
            'fields': ('email', 'password')
        }),
        ('权限设置', {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')
        }),
        ('时间信息', {
            'fields': ('date_joined', 'last_login', 'last_login_ip'),
            'classes': ('collapse',)
        }),
    )

    add_fieldsets = (
        ('基本信息', {
            'classes': ('wide',),
            'fields': ('email', 'password1', 'password2'),
        }),
    )

    ordering = ['-date_joined']
    filter_horizontal = ['groups', 'user_permissions']

    def get_username(self, obj):
        """获取用户名"""
        try:
            return obj.profile.username
        except:
            return '未设置'
    get_username.short_description = '用户名'

    def get_nickname(self, obj):
        """获取昵称"""
        try:
            return obj.profile.nickname or '未设置'
        except:
            return '未设置'
    get_nickname.short_description = '昵称'


@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """用户资料管理"""

    list_display = [
        'username', 'get_email', 'nickname', 'is_email_verified',
        'post_count', 'comment_count', 'like_count', 'created_at'
    ]
    list_filter = [
        'is_email_verified', 'is_phone_verified',
        'receive_email_notifications', 'receive_sms_notifications',
        'created_at'
    ]
    search_fields = ['username', 'nickname', 'user__email', 'phone']
    readonly_fields = [
        'user', 'post_count', 'comment_count', 'like_count',
        'created_at', 'updated_at'
    ]

    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'username', 'nickname', 'avatar', 'bio')
        }),
        ('联系信息', {
            'fields': ('phone', 'expected_delivery_date')
        }),
        ('验证状态', {
            'fields': ('is_email_verified', 'is_phone_verified')
        }),
        ('通知设置', {
            'fields': ('receive_email_notifications', 'receive_sms_notifications')
        }),
        ('统计信息', {
            'fields': ('post_count', 'comment_count', 'like_count'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_email(self, obj):
        """获取邮箱"""
        return obj.user.email
    get_email.short_description = '邮箱'


@admin.register(EmailVerificationCode)
class EmailVerificationCodeAdmin(admin.ModelAdmin):
    """邮箱验证码管理"""

    list_display = [
        'email', 'code', 'code_type', 'is_used',
        'created_at', 'expires_at', 'is_expired_display'
    ]
    list_filter = [
        'code_type', 'is_used', 'created_at', 'expires_at'
    ]
    search_fields = ['email', 'code', 'ip_address']
    readonly_fields = [
        'id', 'created_at', 'used_at', 'is_expired_display'
    ]

    fieldsets = (
        ('基本信息', {
            'fields': ('email', 'code', 'code_type')
        }),
        ('状态信息', {
            'fields': ('is_used', 'used_at', 'expires_at', 'is_expired_display')
        }),
        ('安全信息', {
            'fields': ('ip_address', 'user_agent'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
        ('系统信息', {
            'fields': ('id',),
            'classes': ('collapse',)
        }),
    )

    def is_expired_display(self, obj):
        """过期状态显示"""
        if obj.is_expired():
            return format_html('<span style="color: red;">已过期</span>')
        return format_html('<span style="color: green;">有效</span>')
    is_expired_display.short_description = '是否过期'

    ordering = ['-created_at']


@admin.register(LoginLog)
class LoginLogAdmin(admin.ModelAdmin):
    """登录日志管理"""

    list_display = [
        'email', 'user_link', 'login_type', 'status',
        'ip_address', 'location', 'created_at'
    ]
    list_filter = [
        'login_type', 'status', 'created_at'
    ]
    search_fields = [
        'email', 'user__username', 'ip_address',
        'location', 'failure_reason'
    ]
    readonly_fields = [
        'id', 'created_at', 'user_agent_display'
    ]

    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'email', 'login_type', 'status')
        }),
        ('请求信息', {
            'fields': ('ip_address', 'location', 'user_agent_display')
        }),
        ('失败信息', {
            'fields': ('failure_reason',)
        }),
        ('时间信息', {
            'fields': ('created_at',)
        }),
        ('系统信息', {
            'fields': ('id',),
            'classes': ('collapse',)
        }),
    )

    def user_link(self, obj):
        """用户链接"""
        if obj.user:
            from django.urls import reverse
            url = reverse('admin:users_user_change', args=[obj.user.pk])
            return format_html('<a href="{}">{}</a>', url, obj.user.username)
        return '未知用户'
    user_link.short_description = '用户'

    def user_agent_display(self, obj):
        """用户代理显示"""
        if obj.user_agent:
            return obj.user_agent[:100] + '...' if len(obj.user_agent) > 100 else obj.user_agent
        return '未知'
    user_agent_display.short_description = '用户代理'

    ordering = ['-created_at']

    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('user')
