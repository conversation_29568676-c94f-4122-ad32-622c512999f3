from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe
from .models import Post, PostImage, Comment, PostLike, CommentLike


class PostImageInline(admin.TabularInline):
    """帖子图片内联编辑"""
    model = PostImage
    extra = 1
    fields = ['image', 'caption', 'order']
    readonly_fields = ['created_at']


@admin.register(Post)
class PostAdmin(admin.ModelAdmin):
    """帖子管理"""

    list_display = [
        'title', 'author', 'status', 'view_count', 'like_count',
        'comment_count', 'is_pinned', 'is_featured', 'created_at'
    ]
    list_filter = [
        'status', 'is_pinned', 'is_featured', 'allow_comments',
        'created_at', 'updated_at', 'published_at'
    ]
    search_fields = ['title', 'content', 'author__username', 'author__email']
    readonly_fields = [
        'id', 'slug', 'view_count', 'like_count', 'comment_count',
        'created_at', 'updated_at', 'published_at'
    ]

    fieldsets = (
        ('基本信息', {
            'fields': ('title', 'content', 'excerpt', 'author')
        }),
        ('状态设置', {
            'fields': ('status', 'is_pinned', 'is_featured', 'allow_comments')
        }),
        ('SEO设置', {
            'fields': ('slug', 'meta_description'),
            'classes': ('collapse',)
        }),
        ('统计信息', {
            'fields': ('view_count', 'like_count', 'comment_count'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at', 'published_at'),
            'classes': ('collapse',)
        }),
        ('系统信息', {
            'fields': ('id',),
            'classes': ('collapse',)
        }),
    )

    inlines = [PostImageInline]

    # 列表页面配置
    list_per_page = 20
    list_max_show_all = 100
    date_hierarchy = 'created_at'
    ordering = ['-created_at']

    # 操作配置
    actions = ['make_published', 'make_hidden', 'make_pinned', 'make_unpinned']

    def make_published(self, request, queryset):
        """批量发布"""
        updated = queryset.update(status='published')
        self.message_user(request, f'成功发布 {updated} 篇帖子')
    make_published.short_description = '发布选中的帖子'

    def make_hidden(self, request, queryset):
        """批量隐藏"""
        updated = queryset.update(status='hidden')
        self.message_user(request, f'成功隐藏 {updated} 篇帖子')
    make_hidden.short_description = '隐藏选中的帖子'

    def make_pinned(self, request, queryset):
        """批量置顶"""
        updated = queryset.update(is_pinned=True)
        self.message_user(request, f'成功置顶 {updated} 篇帖子')
    make_pinned.short_description = '置顶选中的帖子'

    def make_unpinned(self, request, queryset):
        """取消置顶"""
        updated = queryset.update(is_pinned=False)
        self.message_user(request, f'成功取消置顶 {updated} 篇帖子')
    make_unpinned.short_description = '取消置顶选中的帖子'

    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('author')


@admin.register(PostImage)
class PostImageAdmin(admin.ModelAdmin):
    """帖子图片管理"""

    list_display = ['post', 'image_preview', 'caption', 'order', 'created_at']
    list_filter = ['created_at', 'post__status']
    search_fields = ['post__title', 'caption']
    readonly_fields = ['id', 'created_at', 'image_preview']

    fieldsets = (
        ('基本信息', {
            'fields': ('post', 'image', 'image_preview', 'caption', 'order')
        }),
        ('时间信息', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
        ('系统信息', {
            'fields': ('id',),
            'classes': ('collapse',)
        }),
    )

    def image_preview(self, obj):
        """图片预览"""
        if obj.image:
            return format_html(
                '<img src="{}" style="max-width: 200px; max-height: 200px;" />',
                obj.image.url
            )
        return '无图片'
    image_preview.short_description = '图片预览'

    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('post')


@admin.register(Comment)
class CommentAdmin(admin.ModelAdmin):
    """评论管理"""

    list_display = [
        'content_preview', 'author', 'post_link', 'parent_comment',
        'status', 'like_count', 'reply_count', 'created_at'
    ]
    list_filter = [
        'status', 'created_at', 'updated_at',
        'post__status'
    ]
    search_fields = [
        'content', 'author__username', 'author__email',
        'post__title', 'reply_to__username'
    ]
    readonly_fields = [
        'id', 'like_count', 'reply_count', 'level_display',
        'created_at', 'updated_at', 'ip_address', 'user_agent'
    ]

    fieldsets = (
        ('基本信息', {
            'fields': ('content', 'author', 'post')
        }),
        ('回复设置', {
            'fields': ('parent', 'reply_to')
        }),
        ('状态设置', {
            'fields': ('status',)
        }),
        ('统计信息', {
            'fields': ('like_count', 'reply_count', 'level_display'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
        ('技术信息', {
            'fields': ('ip_address', 'user_agent'),
            'classes': ('collapse',)
        }),
        ('系统信息', {
            'fields': ('id',),
            'classes': ('collapse',)
        }),
    )

    # 列表页面配置
    list_per_page = 20
    date_hierarchy = 'created_at'
    ordering = ['-created_at']

    # 操作配置
    actions = ['make_published', 'make_hidden', 'make_pending']

    def content_preview(self, obj):
        """内容预览"""
        return obj.content[:50] + '...' if len(obj.content) > 50 else obj.content
    content_preview.short_description = '评论内容'

    def post_link(self, obj):
        """帖子链接"""
        url = reverse('admin:forum_post_change', args=[obj.post.pk])
        return format_html('<a href="{}">{}</a>', url, obj.post.title[:30])
    post_link.short_description = '所属帖子'

    def parent_comment(self, obj):
        """父评论"""
        if obj.parent:
            url = reverse('admin:forum_comment_change', args=[obj.parent.pk])
            content = obj.parent.content[:30] + '...' if len(obj.parent.content) > 30 else obj.parent.content
            return format_html('<a href="{}">{}</a>', url, content)
        return '顶级评论'
    parent_comment.short_description = '父评论'

    def level_display(self, obj):
        """层级显示"""
        return f'第 {obj.level + 1} 层'
    level_display.short_description = '评论层级'

    def make_published(self, request, queryset):
        """批量发布"""
        updated = queryset.update(status='published')
        self.message_user(request, f'成功发布 {updated} 条评论')
    make_published.short_description = '发布选中的评论'

    def make_hidden(self, request, queryset):
        """批量隐藏"""
        updated = queryset.update(status='hidden')
        self.message_user(request, f'成功隐藏 {updated} 条评论')
    make_hidden.short_description = '隐藏选中的评论'

    def make_pending(self, request, queryset):
        """批量待审核"""
        updated = queryset.update(status='pending')
        self.message_user(request, f'成功设置 {updated} 条评论为待审核')
    make_pending.short_description = '设置为待审核'

    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related(
            'author', 'post', 'parent', 'reply_to'
        )


@admin.register(PostLike)
class PostLikeAdmin(admin.ModelAdmin):
    """帖子点赞管理"""

    list_display = ['user', 'post_link', 'created_at']
    list_filter = ['created_at', 'post__status']
    search_fields = ['user__username', 'user__email', 'post__title']
    readonly_fields = ['id', 'created_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'post')
        }),
        ('时间信息', {
            'fields': ('created_at',)
        }),
        ('系统信息', {
            'fields': ('id',),
            'classes': ('collapse',)
        }),
    )

    def post_link(self, obj):
        """帖子链接"""
        url = reverse('admin:forum_post_change', args=[obj.post.pk])
        return format_html('<a href="{}">{}</a>', url, obj.post.title[:30])
    post_link.short_description = '帖子'

    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('user', 'post')


@admin.register(CommentLike)
class CommentLikeAdmin(admin.ModelAdmin):
    """评论点赞管理"""

    list_display = ['user', 'comment_preview', 'created_at']
    list_filter = ['created_at', 'comment__status']
    search_fields = ['user__username', 'user__email', 'comment__content']
    readonly_fields = ['id', 'created_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('user', 'comment')
        }),
        ('时间信息', {
            'fields': ('created_at',)
        }),
        ('系统信息', {
            'fields': ('id',),
            'classes': ('collapse',)
        }),
    )

    def comment_preview(self, obj):
        """评论预览"""
        content = obj.comment.content[:30] + '...' if len(obj.comment.content) > 30 else obj.comment.content
        url = reverse('admin:forum_comment_change', args=[obj.comment.pk])
        return format_html('<a href="{}">{}</a>', url, content)
    comment_preview.short_description = '评论'

    def get_queryset(self, request):
        """优化查询"""
        return super().get_queryset(request).select_related('user', 'comment')
