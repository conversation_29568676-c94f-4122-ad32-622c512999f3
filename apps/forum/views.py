from rest_framework import status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import ModelViewSet
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from drf_spectacular.utils import extend_schema, extend_schema_view
from drf_spectacular.openapi import OpenApiParameter
from django.db.models import F, Q
from django.shortcuts import get_object_or_404

from .models import Post, PostStatus, Comment, CommentStatus, PostLike, CommentLike
from .serializers import (
    PostSerializer, PostListSerializer, PostDetailSerializer,
    PostCreateSerializer, PostUpdateSerializer,
    CommentSerializer, CommentCreateSerializer, CommentUpdateSerializer,
    PostLikeSerializer, CommentLikeSerializer, PostStatsSerializer
)


@extend_schema_view(
    list=extend_schema(
        summary="获取帖子列表",
        description="获取论坛帖子列表，支持搜索、过滤和排序",
        parameters=[
            OpenApiParameter(
                name='search',
                description='搜索帖子标题和内容',
                required=False,
                type=str
            ),
            OpenApiParameter(
                name='status',
                description='帖子状态过滤',
                required=False,
                type=str,
                enum=['draft', 'published', 'hidden', 'deleted']
            ),
            OpenApiParameter(
                name='author',
                description='按作者ID过滤',
                required=False,
                type=int
            ),
            OpenApiParameter(
                name='is_pinned',
                description='是否置顶',
                required=False,
                type=bool
            ),
            OpenApiParameter(
                name='is_featured',
                description='是否精选',
                required=False,
                type=bool
            ),
        ],
        tags=['posts']
    ),
    retrieve=extend_schema(
        summary="获取帖子详情",
        description="根据帖子ID获取详细信息，会自动增加浏览次数",
        tags=['posts']
    ),
    create=extend_schema(
        summary="创建帖子",
        description="创建新的论坛帖子",
        tags=['posts']
    ),
    update=extend_schema(
        summary="更新帖子",
        description="更新帖子信息（仅作者或管理员）",
        tags=['posts']
    ),
    partial_update=extend_schema(
        summary="部分更新帖子",
        description="部分更新帖子信息（仅作者或管理员）",
        tags=['posts']
    ),
    destroy=extend_schema(
        summary="删除帖子",
        description="删除帖子（仅作者或管理员）",
        tags=['posts']
    ),
)
class PostViewSet(ModelViewSet):
    """帖子管理视图集"""

    queryset = Post.objects.all()
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    search_fields = ['title', 'content', 'excerpt']
    filterset_fields = ['status', 'author', 'is_pinned', 'is_featured']
    ordering_fields = ['created_at', 'updated_at', 'published_at', 'view_count', 'like_count', 'comment_count']
    ordering = ['-is_pinned', '-created_at']

    def get_serializer_class(self):
        if self.action == 'list':
            return PostListSerializer
        elif self.action == 'create':
            return PostCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return PostUpdateSerializer
        else:
            return PostDetailSerializer

    def get_permissions(self):
        """根据操作设置权限"""
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.AllowAny]
        elif self.action == 'create':
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [permissions.IsAuthenticated]

        return [permission() for permission in permission_classes]

    def get_queryset(self):
        """根据用户权限过滤查询集"""
        queryset = super().get_queryset()

        # 未认证用户只能看到已发布的帖子
        if not self.request.user.is_authenticated:
            return queryset.filter(status=PostStatus.PUBLISHED)

        # 普通用户可以看到已发布的帖子和自己的帖子
        if not self.request.user.is_staff:
            return queryset.filter(
                Q(status=PostStatus.PUBLISHED) | Q(author=self.request.user)
            )

        # 管理员可以看到所有帖子
        return queryset

    def retrieve(self, request, *args, **kwargs):
        """获取帖子详情，增加浏览次数"""
        instance = self.get_object()

        # 增加浏览次数（避免作者自己浏览增加次数）
        if not request.user.is_authenticated or instance.author != request.user:
            instance.increment_view_count()

        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def perform_create(self, serializer):
        """创建帖子时设置作者"""
        serializer.save(author=self.request.user)

    def perform_update(self, serializer):
        """更新时检查权限"""
        instance = self.get_object()
        if instance.author != self.request.user and not self.request.user.is_staff:
            raise permissions.PermissionDenied("只能修改自己的帖子")
        serializer.save()

    def perform_destroy(self, instance):
        """删除时检查权限"""
        if instance.author != self.request.user and not self.request.user.is_staff:
            raise permissions.PermissionDenied("只能删除自己的帖子")
        instance.status = PostStatus.DELETED
        instance.save()

    @extend_schema(
        summary="点赞/取消点赞帖子",
        description="对帖子进行点赞或取消点赞操作",
        responses={
            200: {
                "type": "object",
                "properties": {
                    "liked": {"type": "boolean", "description": "是否已点赞"},
                    "like_count": {"type": "integer", "description": "总点赞数"}
                }
            }
        },
        tags=['posts']
    )
    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def like(self, request, pk=None):
        """点赞/取消点赞帖子"""
        post = self.get_object()
        user = request.user

        like, created = PostLike.objects.get_or_create(post=post, user=user)

        if not created:
            # 如果已经点赞，则取消点赞
            like.delete()
            post.like_count = F('like_count') - 1
            post.save(update_fields=['like_count'])
            liked = False
        else:
            # 新增点赞
            post.like_count = F('like_count') + 1
            post.save(update_fields=['like_count'])
            liked = True

        # 刷新对象以获取最新的like_count
        post.refresh_from_db()

        return Response({
            'liked': liked,
            'like_count': post.like_count
        })

    @extend_schema(
        summary="获取帖子评论",
        description="获取指定帖子的评论列表",
        parameters=[
            OpenApiParameter(
                name='ordering',
                description='排序方式',
                required=False,
                type=str,
                enum=['created_at', '-created_at', 'like_count', '-like_count']
            ),
        ],
        tags=['posts']
    )
    @action(detail=True, methods=['get'])
    def comments(self, request, pk=None):
        """获取帖子评论"""
        post = self.get_object()
        comments = Comment.objects.filter(
            post=post,
            parent=None,  # 只获取顶级评论
            status=CommentStatus.PUBLISHED
        )

        # 排序
        ordering = request.query_params.get('ordering', '-created_at')
        if ordering in ['created_at', '-created_at', 'like_count', '-like_count']:
            comments = comments.order_by(ordering)
        else:
            comments = comments.order_by('-created_at')

        # 分页
        page = self.paginate_queryset(comments)
        if page is not None:
            serializer = CommentSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)

        serializer = CommentSerializer(comments, many=True, context={'request': request})
        return Response(serializer.data)

    @extend_schema(
        summary="创建帖子评论",
        description="为指定帖子创建评论",
        request=CommentCreateSerializer,
        responses={201: CommentSerializer},
        tags=['posts']
    )
    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def create_comment(self, request, pk=None):
        """创建帖子评论"""
        post = self.get_object()

        if not post.allow_comments:
            return Response(
                {"detail": "该帖子不允许评论"},
                status=status.HTTP_403_FORBIDDEN
            )

        serializer = CommentCreateSerializer(
            data=request.data,
            context={'request': request, 'post': post}
        )
        if serializer.is_valid():
            comment = serializer.save()
            response_serializer = CommentSerializer(comment, context={'request': request})
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @extend_schema(
        summary="获取帖子统计信息",
        description="获取论坛的整体统计信息",
        responses={200: PostStatsSerializer},
        tags=['posts']
    )
    @action(detail=False, methods=['get'], permission_classes=[permissions.IsAuthenticated])
    def stats(self, request):
        """获取帖子统计信息"""
        from django.db.models import Count, Sum

        stats = Post.objects.aggregate(
            total_posts=Count('id'),
            published_posts=Count('id', filter=Q(status=PostStatus.PUBLISHED)),
            draft_posts=Count('id', filter=Q(status=PostStatus.DRAFT)),
            total_views=Sum('view_count'),
            total_likes=Sum('like_count'),
            total_comments=Sum('comment_count')
        )

        # 处理None值
        for key, value in stats.items():
            if value is None:
                stats[key] = 0

        serializer = PostStatsSerializer(stats)
        return Response(serializer.data)


@extend_schema_view(
    list=extend_schema(
        summary="获取评论列表",
        description="获取评论列表（管理员功能）",
        tags=['comments']
    ),
    retrieve=extend_schema(
        summary="获取评论详情",
        description="获取指定评论的详细信息",
        tags=['comments']
    ),
    update=extend_schema(
        summary="更新评论",
        description="更新评论内容（仅作者或管理员）",
        tags=['comments']
    ),
    partial_update=extend_schema(
        summary="部分更新评论",
        description="部分更新评论内容（仅作者或管理员）",
        tags=['comments']
    ),
    destroy=extend_schema(
        summary="删除评论",
        description="删除评论（仅作者或管理员）",
        tags=['comments']
    ),
)
class CommentViewSet(ModelViewSet):
    """评论管理视图集"""

    queryset = Comment.objects.all()
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    search_fields = ['content']
    filterset_fields = ['post', 'author', 'status', 'parent']
    ordering_fields = ['created_at', 'updated_at', 'like_count']
    ordering = ['-created_at']

    def get_serializer_class(self):
        if self.action in ['update', 'partial_update']:
            return CommentUpdateSerializer
        else:
            return CommentSerializer

    def get_permissions(self):
        """根据操作设置权限"""
        if self.action in ['list', 'retrieve']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [permissions.IsAuthenticated]

        return [permission() for permission in permission_classes]

    def get_queryset(self):
        """根据用户权限过滤查询集"""
        queryset = super().get_queryset()

        # 普通用户只能看到已发布的评论和自己的评论
        if not self.request.user.is_staff:
            return queryset.filter(
                Q(status=CommentStatus.PUBLISHED) | Q(author=self.request.user)
            )

        # 管理员可以看到所有评论
        return queryset

    def perform_update(self, serializer):
        """更新时检查权限"""
        instance = self.get_object()
        if instance.author != self.request.user and not self.request.user.is_staff:
            raise permissions.PermissionDenied("只能修改自己的评论")
        serializer.save()

    def perform_destroy(self, instance):
        """删除时检查权限"""
        if instance.author != self.request.user and not self.request.user.is_staff:
            raise permissions.PermissionDenied("只能删除自己的评论")
        instance.status = CommentStatus.DELETED
        instance.save()

    @extend_schema(
        summary="点赞/取消点赞评论",
        description="对评论进行点赞或取消点赞操作",
        responses={
            200: {
                "type": "object",
                "properties": {
                    "liked": {"type": "boolean", "description": "是否已点赞"},
                    "like_count": {"type": "integer", "description": "总点赞数"}
                }
            }
        },
        tags=['comments']
    )
    @action(detail=True, methods=['post'], permission_classes=[permissions.IsAuthenticated])
    def like(self, request, pk=None):
        """点赞/取消点赞评论"""
        comment = self.get_object()
        user = request.user

        like, created = CommentLike.objects.get_or_create(comment=comment, user=user)

        if not created:
            # 如果已经点赞，则取消点赞
            like.delete()
            comment.like_count = F('like_count') - 1
            comment.save(update_fields=['like_count'])
            liked = False
        else:
            # 新增点赞
            comment.like_count = F('like_count') + 1
            comment.save(update_fields=['like_count'])
            liked = True

        # 刷新对象以获取最新的like_count
        comment.refresh_from_db()

        return Response({
            'liked': liked,
            'like_count': comment.like_count
        })

    @extend_schema(
        summary="获取评论回复",
        description="获取指定评论的回复列表",
        tags=['comments']
    )
    @action(detail=True, methods=['get'])
    def replies(self, request, pk=None):
        """获取评论回复"""
        comment = self.get_object()
        replies = comment.replies.filter(status=CommentStatus.PUBLISHED).order_by('created_at')

        serializer = CommentSerializer(replies, many=True, context={'request': request})
        return Response(serializer.data)
