from django.db import models
from django.conf import settings
from django.utils import timezone
from mdeditor.fields import MDTextField
import uuid


class PostStatus(models.TextChoices):
    """帖子状态枚举"""
    DRAFT = 'draft', '草稿'
    PUBLISHED = 'published', '已发布'
    HIDDEN = 'hidden', '已隐藏'
    DELETED = 'deleted', '已删除'


class Post(models.Model):
    """帖子模型"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    title = models.CharField('标题', max_length=200)
    content = MDTextField('正文内容', help_text='支持Markdown格式')
    excerpt = models.TextField('摘要', max_length=500, blank=True, help_text='自动从内容中提取')

    # 作者信息
    author = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        verbose_name='作者',
        related_name='posts'
    )

    # 状态和分类
    status = models.CharField(
        '状态',
        max_length=20,
        choices=PostStatus.choices,
        default=PostStatus.DRAFT
    )

    # 统计信息
    view_count = models.PositiveIntegerField('浏览次数', default=0)
    like_count = models.PositiveIntegerField('点赞数', default=0)
    comment_count = models.PositiveIntegerField('评论数', default=0)

    # 时间戳
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    published_at = models.DateTimeField('发布时间', null=True, blank=True)

    # SEO相关
    slug = models.SlugField('URL别名', max_length=200, blank=True, unique=True)
    meta_description = models.CharField('SEO描述', max_length=160, blank=True)

    # 其他设置
    is_pinned = models.BooleanField('置顶', default=False)
    is_featured = models.BooleanField('精选', default=False)
    allow_comments = models.BooleanField('允许评论', default=True)

    class Meta:
        db_table = 'forum_posts'
        verbose_name = '帖子'
        verbose_name_plural = '帖子'
        ordering = ['-is_pinned', '-created_at']
        indexes = [
            models.Index(fields=['status', 'created_at']),
            models.Index(fields=['author', 'status']),
            models.Index(fields=['is_pinned', 'created_at']),
            models.Index(fields=['published_at']),
        ]

    def __str__(self):
        return self.title

    def save(self, *args, **kwargs):
        # 自动设置发布时间
        if self.status == PostStatus.PUBLISHED and not self.published_at:
            self.published_at = timezone.now()

        # 自动生成摘要
        if not self.excerpt and self.content:
            # 简单的摘要提取，去除Markdown标记
            import re
            clean_content = re.sub(r'[#*`\[\]()_~]', '', self.content)
            self.excerpt = clean_content[:200] + '...' if len(clean_content) > 200 else clean_content

        # 自动生成slug
        if not self.slug:
            from django.utils.text import slugify
            import time
            base_slug = slugify(self.title[:50])
            if not base_slug:
                base_slug = f'post-{int(time.time())}'
            self.slug = base_slug

            # 确保slug唯一
            counter = 1
            while Post.objects.filter(slug=self.slug).exclude(pk=self.pk).exists():
                self.slug = f'{base_slug}-{counter}'
                counter += 1

        super().save(*args, **kwargs)

    @property
    def is_published(self):
        """是否已发布"""
        return self.status == PostStatus.PUBLISHED

    def get_absolute_url(self):
        """获取帖子详情URL"""
        return f'/posts/{self.slug}/'

    def increment_view_count(self):
        """增加浏览次数"""
        self.view_count = models.F('view_count') + 1
        self.save(update_fields=['view_count'])

    def get_comments(self):
        """获取帖子的所有评论（按层级排序）"""
        return self.comments.filter(parent=None).order_by('-created_at')


class PostImage(models.Model):
    """帖子图片模型"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    post = models.ForeignKey(
        Post,
        on_delete=models.CASCADE,
        verbose_name='帖子',
        related_name='images'
    )
    image = models.ImageField('图片', upload_to='posts/images/%Y/%m/')
    caption = models.CharField('图片说明', max_length=200, blank=True)
    order = models.PositiveIntegerField('排序', default=0)

    # 时间戳
    created_at = models.DateTimeField('上传时间', auto_now_add=True)

    class Meta:
        db_table = 'forum_post_images'
        verbose_name = '帖子图片'
        verbose_name_plural = '帖子图片'
        ordering = ['order', 'created_at']

    def __str__(self):
        return f'{self.post.title} - 图片{self.order}'


class CommentStatus(models.TextChoices):
    """评论状态枚举"""
    PUBLISHED = 'published', '已发布'
    PENDING = 'pending', '待审核'
    HIDDEN = 'hidden', '已隐藏'
    DELETED = 'deleted', '已删除'


class Comment(models.Model):
    """评论模型 - 支持层级回复"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    content = models.TextField('评论内容', max_length=1000)

    # 关联信息
    post = models.ForeignKey(
        Post,
        on_delete=models.CASCADE,
        verbose_name='帖子',
        related_name='comments'
    )
    author = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        verbose_name='作者',
        related_name='comments'
    )

    # 层级回复支持
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        verbose_name='父评论',
        related_name='replies'
    )

    # 回复目标用户（@功能）
    reply_to = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name='回复给',
        related_name='received_replies'
    )

    # 状态
    status = models.CharField(
        '状态',
        max_length=20,
        choices=CommentStatus.choices,
        default=CommentStatus.PUBLISHED
    )

    # 统计信息
    like_count = models.PositiveIntegerField('点赞数', default=0)
    reply_count = models.PositiveIntegerField('回复数', default=0)

    # 时间戳
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    # 其他信息
    ip_address = models.GenericIPAddressField('IP地址', null=True, blank=True)
    user_agent = models.TextField('用户代理', blank=True)

    class Meta:
        db_table = 'forum_comments'
        verbose_name = '评论'
        verbose_name_plural = '评论'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['post', 'status', 'created_at']),
            models.Index(fields=['author', 'created_at']),
            models.Index(fields=['parent', 'created_at']),
            models.Index(fields=['status', 'created_at']),
        ]

    def __str__(self):
        return f'{self.author.username} 在 {self.post.title} 的评论'

    def save(self, *args, **kwargs):
        is_new = self.pk is None
        super().save(*args, **kwargs)

        # 如果是新评论，更新帖子的评论数
        if is_new and self.status == CommentStatus.PUBLISHED:
            self.post.comment_count = models.F('comment_count') + 1
            self.post.save(update_fields=['comment_count'])

            # 如果是回复，更新父评论的回复数
            if self.parent:
                self.parent.reply_count = models.F('reply_count') + 1
                self.parent.save(update_fields=['reply_count'])

    def delete(self, *args, **kwargs):
        # 删除评论时更新计数
        if self.status == CommentStatus.PUBLISHED:
            self.post.comment_count = models.F('comment_count') - 1
            self.post.save(update_fields=['comment_count'])

            if self.parent:
                self.parent.reply_count = models.F('reply_count') - 1
                self.parent.save(update_fields=['reply_count'])

        super().delete(*args, **kwargs)

    @property
    def is_published(self):
        """是否已发布"""
        return self.status == CommentStatus.PUBLISHED

    @property
    def level(self):
        """获取评论层级"""
        if not self.parent:
            return 0
        return self.parent.level + 1

    def get_replies(self):
        """获取直接回复"""
        return self.replies.filter(status=CommentStatus.PUBLISHED).order_by('created_at')

    def get_all_replies(self):
        """获取所有子回复（递归）"""
        replies = []
        for reply in self.get_replies():
            replies.append(reply)
            replies.extend(reply.get_all_replies())
        return replies


class PostLike(models.Model):
    """帖子点赞模型"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    post = models.ForeignKey(
        Post,
        on_delete=models.CASCADE,
        verbose_name='帖子',
        related_name='likes'
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        verbose_name='用户',
        related_name='post_likes'
    )
    created_at = models.DateTimeField('点赞时间', auto_now_add=True)

    class Meta:
        db_table = 'forum_post_likes'
        verbose_name = '帖子点赞'
        verbose_name_plural = '帖子点赞'
        unique_together = ['post', 'user']
        indexes = [
            models.Index(fields=['post', 'created_at']),
            models.Index(fields=['user', 'created_at']),
        ]

    def __str__(self):
        return f'{self.user.username} 点赞了 {self.post.title}'

    def save(self, *args, **kwargs):
        is_new = self.pk is None
        super().save(*args, **kwargs)

        if is_new:
            # 更新帖子点赞数
            self.post.like_count = models.F('like_count') + 1
            self.post.save(update_fields=['like_count'])

            # 更新用户获赞数
            self.post.author.like_count = models.F('like_count') + 1
            self.post.author.save(update_fields=['like_count'])

    def delete(self, *args, **kwargs):
        # 更新帖子点赞数
        self.post.like_count = models.F('like_count') - 1
        self.post.save(update_fields=['like_count'])

        # 更新用户获赞数
        self.post.author.like_count = models.F('like_count') - 1
        self.post.author.save(update_fields=['like_count'])

        super().delete(*args, **kwargs)


class CommentLike(models.Model):
    """评论点赞模型"""

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    comment = models.ForeignKey(
        Comment,
        on_delete=models.CASCADE,
        verbose_name='评论',
        related_name='likes'
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        verbose_name='用户',
        related_name='comment_likes'
    )
    created_at = models.DateTimeField('点赞时间', auto_now_add=True)

    class Meta:
        db_table = 'forum_comment_likes'
        verbose_name = '评论点赞'
        verbose_name_plural = '评论点赞'
        unique_together = ['comment', 'user']
        indexes = [
            models.Index(fields=['comment', 'created_at']),
            models.Index(fields=['user', 'created_at']),
        ]

    def __str__(self):
        return f'{self.user.username} 点赞了评论'

    def save(self, *args, **kwargs):
        is_new = self.pk is None
        super().save(*args, **kwargs)

        if is_new:
            # 更新评论点赞数
            self.comment.like_count = models.F('like_count') + 1
            self.comment.save(update_fields=['like_count'])

    def delete(self, *args, **kwargs):
        # 更新评论点赞数
        self.comment.like_count = models.F('like_count') - 1
        self.comment.save(update_fields=['like_count'])

        super().delete(*args, **kwargs)
