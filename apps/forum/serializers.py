from rest_framework import serializers
from django.utils import timezone
from .models import Post, PostStatus, Comment, CommentStatus, PostImage, PostLike, CommentLike
from apps.users.serializers import UserSerializer


class PostImageSerializer(serializers.ModelSerializer):
    """帖子图片序列化器"""
    
    class Meta:
        model = PostImage
        fields = ['id', 'image', 'caption', 'order']
        read_only_fields = ['id']


class PostSerializer(serializers.ModelSerializer):
    """帖子基本信息序列化器"""
    
    author = UserSerializer(read_only=True)
    images = PostImageSerializer(many=True, read_only=True)
    is_liked = serializers.SerializerMethodField()
    
    class Meta:
        model = Post
        fields = [
            'id', 'title', 'content', 'excerpt', 'author', 'status',
            'view_count', 'like_count', 'comment_count', 'created_at',
            'updated_at', 'published_at', 'slug', 'is_pinned', 'is_featured',
            'allow_comments', 'images', 'is_liked'
        ]
        read_only_fields = [
            'id', 'author', 'view_count', 'like_count', 'comment_count',
            'created_at', 'updated_at', 'published_at', 'slug'
        ]
    
    def get_is_liked(self, obj):
        """检查当前用户是否已点赞"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return PostLike.objects.filter(post=obj, user=request.user).exists()
        return False


class PostListSerializer(PostSerializer):
    """帖子列表序列化器（简化版）"""
    
    class Meta(PostSerializer.Meta):
        fields = [
            'id', 'title', 'excerpt', 'author', 'status', 'view_count',
            'like_count', 'comment_count', 'created_at', 'published_at',
            'is_pinned', 'is_featured', 'is_liked'
        ]


class PostDetailSerializer(PostSerializer):
    """帖子详情序列化器"""
    
    class Meta(PostSerializer.Meta):
        fields = PostSerializer.Meta.fields + ['meta_description']


class PostCreateSerializer(serializers.ModelSerializer):
    """创建帖子序列化器"""
    
    images = PostImageSerializer(many=True, required=False)
    
    class Meta:
        model = Post
        fields = [
            'title', 'content', 'status', 'is_pinned', 'is_featured',
            'allow_comments', 'meta_description', 'images'
        ]
    
    def create(self, validated_data):
        images_data = validated_data.pop('images', [])
        post = Post.objects.create(**validated_data)
        
        # 创建图片
        for image_data in images_data:
            PostImage.objects.create(post=post, **image_data)
        
        return post


class PostUpdateSerializer(serializers.ModelSerializer):
    """更新帖子序列化器"""
    
    images = PostImageSerializer(many=True, required=False)
    
    class Meta:
        model = Post
        fields = [
            'title', 'content', 'status', 'allow_comments',
            'meta_description', 'images'
        ]
    
    def update(self, instance, validated_data):
        images_data = validated_data.pop('images', None)
        
        # 更新帖子基本信息
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # 更新图片（如果提供）
        if images_data is not None:
            # 删除旧图片
            instance.images.all().delete()
            # 创建新图片
            for image_data in images_data:
                PostImage.objects.create(post=instance, **image_data)
        
        return instance


class CommentSerializer(serializers.ModelSerializer):
    """评论序列化器"""
    
    author = UserSerializer(read_only=True)
    reply_to = UserSerializer(read_only=True)
    replies = serializers.SerializerMethodField()
    is_liked = serializers.SerializerMethodField()
    
    class Meta:
        model = Comment
        fields = [
            'id', 'content', 'author', 'parent', 'reply_to', 'status',
            'like_count', 'reply_count', 'created_at', 'updated_at',
            'replies', 'is_liked'
        ]
        read_only_fields = [
            'id', 'author', 'like_count', 'reply_count',
            'created_at', 'updated_at'
        ]
    
    def get_replies(self, obj):
        """获取回复列表"""
        if obj.replies.exists():
            return CommentSerializer(
                obj.replies.filter(status=CommentStatus.PUBLISHED).order_by('created_at'),
                many=True,
                context=self.context
            ).data
        return []
    
    def get_is_liked(self, obj):
        """检查当前用户是否已点赞"""
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return CommentLike.objects.filter(comment=obj, user=request.user).exists()
        return False


class CommentCreateSerializer(serializers.ModelSerializer):
    """创建评论序列化器"""
    
    class Meta:
        model = Comment
        fields = ['content', 'parent', 'reply_to']
    
    def validate(self, attrs):
        parent = attrs.get('parent')
        reply_to = attrs.get('reply_to')
        
        # 如果有父评论，reply_to必须是父评论的作者或其回复中的某个作者
        if parent and reply_to:
            # 检查reply_to是否合理
            valid_users = [parent.author]
            valid_users.extend([reply.author for reply in parent.replies.all()])
            
            if reply_to not in valid_users:
                raise serializers.ValidationError("回复目标用户不正确")
        
        return attrs
    
    def create(self, validated_data):
        post = self.context['post']
        comment = Comment.objects.create(
            post=post,
            author=self.context['request'].user,
            **validated_data
        )
        return comment


class CommentUpdateSerializer(serializers.ModelSerializer):
    """更新评论序列化器"""
    
    class Meta:
        model = Comment
        fields = ['content']


class PostLikeSerializer(serializers.ModelSerializer):
    """帖子点赞序列化器"""
    
    class Meta:
        model = PostLike
        fields = ['id', 'created_at']
        read_only_fields = ['id', 'created_at']


class CommentLikeSerializer(serializers.ModelSerializer):
    """评论点赞序列化器"""
    
    class Meta:
        model = CommentLike
        fields = ['id', 'created_at']
        read_only_fields = ['id', 'created_at']


class PostStatsSerializer(serializers.Serializer):
    """帖子统计信息序列化器"""
    
    total_posts = serializers.IntegerField()
    published_posts = serializers.IntegerField()
    draft_posts = serializers.IntegerField()
    total_views = serializers.IntegerField()
    total_likes = serializers.IntegerField()
    total_comments = serializers.IntegerField()
