# Generated by Django 5.2.4 on 2025-07-11 14:48

import django.db.models.deletion
import mdeditor.fields
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Post',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200, verbose_name='标题')),
                ('content', mdeditor.fields.MDTextField(help_text='支持Markdown格式', verbose_name='正文内容')),
                ('excerpt', models.TextField(blank=True, help_text='自动从内容中提取', max_length=500, verbose_name='摘要')),
                ('status', models.CharField(choices=[('draft', '草稿'), ('published', '已发布'), ('hidden', '已隐藏'), ('deleted', '已删除')], default='draft', max_length=20, verbose_name='状态')),
                ('view_count', models.PositiveIntegerField(default=0, verbose_name='浏览次数')),
                ('like_count', models.PositiveIntegerField(default=0, verbose_name='点赞数')),
                ('comment_count', models.PositiveIntegerField(default=0, verbose_name='评论数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('published_at', models.DateTimeField(blank=True, null=True, verbose_name='发布时间')),
                ('slug', models.SlugField(blank=True, max_length=200, unique=True, verbose_name='URL别名')),
                ('meta_description', models.CharField(blank=True, max_length=160, verbose_name='SEO描述')),
                ('is_pinned', models.BooleanField(default=False, verbose_name='置顶')),
                ('is_featured', models.BooleanField(default=False, verbose_name='精选')),
                ('allow_comments', models.BooleanField(default=True, verbose_name='允许评论')),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='posts', to=settings.AUTH_USER_MODEL, verbose_name='作者')),
            ],
            options={
                'verbose_name': '帖子',
                'verbose_name_plural': '帖子',
                'db_table': 'forum_posts',
                'ordering': ['-is_pinned', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Comment',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('content', models.TextField(max_length=1000, verbose_name='评论内容')),
                ('status', models.CharField(choices=[('published', '已发布'), ('pending', '待审核'), ('hidden', '已隐藏'), ('deleted', '已删除')], default='published', max_length=20, verbose_name='状态')),
                ('like_count', models.PositiveIntegerField(default=0, verbose_name='点赞数')),
                ('reply_count', models.PositiveIntegerField(default=0, verbose_name='回复数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP地址')),
                ('user_agent', models.TextField(blank=True, verbose_name='用户代理')),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to=settings.AUTH_USER_MODEL, verbose_name='作者')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='replies', to='forum.comment', verbose_name='父评论')),
                ('reply_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='received_replies', to=settings.AUTH_USER_MODEL, verbose_name='回复给')),
                ('post', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to='forum.post', verbose_name='帖子')),
            ],
            options={
                'verbose_name': '评论',
                'verbose_name_plural': '评论',
                'db_table': 'forum_comments',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PostImage',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('image', models.ImageField(upload_to='posts/images/%Y/%m/', verbose_name='图片')),
                ('caption', models.CharField(blank=True, max_length=200, verbose_name='图片说明')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='排序')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='上传时间')),
                ('post', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='forum.post', verbose_name='帖子')),
            ],
            options={
                'verbose_name': '帖子图片',
                'verbose_name_plural': '帖子图片',
                'db_table': 'forum_post_images',
                'ordering': ['order', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='PostLike',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='点赞时间')),
                ('post', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='likes', to='forum.post', verbose_name='帖子')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='post_likes', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '帖子点赞',
                'verbose_name_plural': '帖子点赞',
                'db_table': 'forum_post_likes',
            },
        ),
        migrations.CreateModel(
            name='CommentLike',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='点赞时间')),
                ('comment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='likes', to='forum.comment', verbose_name='评论')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comment_likes', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '评论点赞',
                'verbose_name_plural': '评论点赞',
                'db_table': 'forum_comment_likes',
                'indexes': [models.Index(fields=['comment', 'created_at'], name='forum_comme_comment_7008f1_idx'), models.Index(fields=['user', 'created_at'], name='forum_comme_user_id_273746_idx')],
                'unique_together': {('comment', 'user')},
            },
        ),
        migrations.AddIndex(
            model_name='post',
            index=models.Index(fields=['status', 'created_at'], name='forum_posts_status_5722ef_idx'),
        ),
        migrations.AddIndex(
            model_name='post',
            index=models.Index(fields=['author', 'status'], name='forum_posts_author__fba486_idx'),
        ),
        migrations.AddIndex(
            model_name='post',
            index=models.Index(fields=['is_pinned', 'created_at'], name='forum_posts_is_pinn_e1371c_idx'),
        ),
        migrations.AddIndex(
            model_name='post',
            index=models.Index(fields=['published_at'], name='forum_posts_publish_d07627_idx'),
        ),
        migrations.AddIndex(
            model_name='comment',
            index=models.Index(fields=['post', 'status', 'created_at'], name='forum_comme_post_id_b8b1b0_idx'),
        ),
        migrations.AddIndex(
            model_name='comment',
            index=models.Index(fields=['author', 'created_at'], name='forum_comme_author__b71dfd_idx'),
        ),
        migrations.AddIndex(
            model_name='comment',
            index=models.Index(fields=['parent', 'created_at'], name='forum_comme_parent__ef3246_idx'),
        ),
        migrations.AddIndex(
            model_name='comment',
            index=models.Index(fields=['status', 'created_at'], name='forum_comme_status_cf9237_idx'),
        ),
        migrations.AddIndex(
            model_name='postlike',
            index=models.Index(fields=['post', 'created_at'], name='forum_post__post_id_d38f0e_idx'),
        ),
        migrations.AddIndex(
            model_name='postlike',
            index=models.Index(fields=['user', 'created_at'], name='forum_post__user_id_f2c63f_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='postlike',
            unique_together={('post', 'user')},
        ),
    ]
